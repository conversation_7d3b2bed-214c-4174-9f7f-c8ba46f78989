import { ServedAd } from "@/lib/db/models";

// Database ad slot IDs and their configurations
export const AD_SLOTS = {
	// Home Page Slots (1-4)
	HOME_LEADERBOARD: 1, // 728x90
	HOME_BILLBOARD: 2, // 970x250
	HOME_MEDIUM_RECTANGLE: 3, // 300x250
	HOME_SKYSCRAPER: 4, // 160x600

	// Subnet Page Slots (7-10)
	SUBNET_MEDIUM_RECTANGLE: 7, // 300x250
	SUBNET_HALF_PAGE: 8, // 300x600
	SUBNET_BANNER: 9, // 468x60
	SUBNET_WIDE_SKYSCRAPER: 10, // 160x600

	// Company Page Slots (11-14)
	COMPANY_MEDIUM_RECTANGLE: 11, // 300x250
	COMPANY_HALF_PAGE: 12, // 300x600
	COMPANY_LEADERBOARD: 13, // 728x90
	COMPANY_SQUARE_BUTTON: 14, // 125x125

	// Global Slots (15-16)
	GLOBAL_POPUP: 15, // 400x400
	GLOBAL_STICKY_FOOTER: 16, // 320x50
} as const;

// Slot dimensions mapping
export const SLOT_DIMENSIONS = {
	[AD_SLOTS.HOME_LEADERBOARD]: { width: 728, height: 90 },
	[AD_SLOTS.HOME_BILLBOARD]: { width: 970, height: 250 },
	[AD_SLOTS.HOME_MEDIUM_RECTANGLE]: { width: 300, height: 250 },
	[AD_SLOTS.HOME_SKYSCRAPER]: { width: 160, height: 600 },
	[AD_SLOTS.SUBNET_MEDIUM_RECTANGLE]: { width: 300, height: 250 },
	[AD_SLOTS.SUBNET_HALF_PAGE]: { width: 300, height: 600 },
	[AD_SLOTS.SUBNET_BANNER]: { width: 468, height: 60 },
	[AD_SLOTS.SUBNET_WIDE_SKYSCRAPER]: { width: 160, height: 600 },
	[AD_SLOTS.COMPANY_MEDIUM_RECTANGLE]: { width: 300, height: 250 },
	[AD_SLOTS.COMPANY_HALF_PAGE]: { width: 300, height: 600 },
	[AD_SLOTS.COMPANY_LEADERBOARD]: { width: 728, height: 90 },
	[AD_SLOTS.COMPANY_SQUARE_BUTTON]: { width: 125, height: 125 },
	[AD_SLOTS.GLOBAL_POPUP]: { width: 400, height: 400 },
	[AD_SLOTS.GLOBAL_STICKY_FOOTER]: { width: 320, height: 50 },
} as const;

// Google AdSense slots - each slot has a unique ID to avoid conflicts
export const GOOGLE_AD_SLOTS = {
	[AD_SLOTS.HOME_LEADERBOARD]: "3369057847", // 728x90
	[AD_SLOTS.HOME_BILLBOARD]: "6182923442", // 970x250
	[AD_SLOTS.HOME_MEDIUM_RECTANGLE]: "7844510005", // 300x250
	[AD_SLOTS.HOME_SKYSCRAPER]: "5254383953", // 160x600
	[AD_SLOTS.SUBNET_MEDIUM_RECTANGLE]: "3064454441", // 300x250
	[AD_SLOTS.SUBNET_HALF_PAGE]: "9249469283", // 300x600
	[AD_SLOTS.SUBNET_BANNER]: "9765594162", // 468x60
	[AD_SLOTS.SUBNET_WIDE_SKYSCRAPER]: "6623305948", // 160x600
	[AD_SLOTS.COMPANY_MEDIUM_RECTANGLE]: "3997142602", // 300x250
	[AD_SLOTS.COMPANY_HALF_PAGE]: "8621384528", // 300x600
	[AD_SLOTS.COMPANY_LEADERBOARD]: "7308302852", // 728x90
	[AD_SLOTS.COMPANY_SQUARE_BUTTON]: "2378058731", // 125x125
	[AD_SLOTS.GLOBAL_POPUP]: "5445955649", // 400x400
	[AD_SLOTS.GLOBAL_STICKY_FOOTER]: "1064977062", // 320x50
} as const;

export interface AdServeRequest {
	slot: number;
	country_code?: string;
	device_type?: string;
	language?: string;
	user_agent?: string;
}

export interface AdServeResponse {
	success: boolean;
	data?: ServedAd;
	message: string;
}

export interface AdImpressionRequest {
	ad_id: number;
	session_id: string;
	user_id?: number;
	country_code?: string;
	device_type?: string;
	viewed_time?: number;
}

export interface AdClickRequest {
	ad_id: number;
	session_id: string;
	user_id?: number;
	country_code?: string;
	device_type?: string;
}

/**
 * CLIENT-SIDE
 * Track ad impression
 */
export async function trackAdImpression(impressionData: AdImpressionRequest): Promise<boolean> {
	try {
		const response = await fetch("/api/ads/track/impression", {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
			},
			body: JSON.stringify(impressionData),
		});

		return response.ok;
	} catch (error) {
		console.error("Error tracking impression:", error);
		return false;
	}
}

/**
 * CLIENT-SIDE
 * Track ad click and redirect
 */
export async function trackAdClick(clickData: AdClickRequest): Promise<boolean> {
	try {
		const response = await fetch("/api/ads/track/click", {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
			},
			body: JSON.stringify(clickData),
		});

		return response.ok;
	} catch (error) {
		console.error("Error tracking click:", error);
		return false;
	}
}

/**
 * Generate a session ID for ad tracking
 */
export function generateSessionId(): string {
	// Generate a UUID v4 compatible string
	return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
		const r = (Math.random() * 16) | 0;
		const v = c == "x" ? r : (r & 0x3) | 0x8;
		return v.toString(16);
	});
}

/**
 * Get device type from user agent
 */
export function getDeviceType(userAgent?: string): string {
	if (!userAgent) return "unknown";

	const ua = userAgent.toLowerCase();
	if (ua.includes("mobile") || ua.includes("android") || ua.includes("iphone")) {
		return "mobile";
	}
	if (ua.includes("tablet") || ua.includes("ipad")) {
		return "tablet";
	}
	return "desktop";
}

/**
 * Get user's country code (placeholder - would integrate with geolocation service)
 */
export function getCountryCode(): string {
	// In a real implementation, this would use a geolocation service
	// For now, return a default
	return "US";
}

/**
 * Get user's language preference
 */
export function getLanguage(): string {
	if (typeof window !== "undefined") {
		return navigator.language || "en-US";
	}
	return "en-US";
}
