"use client";

import { <PERSON>ert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { CheckCircle, CreditCard, History, Info, Target, XCircle } from "lucide-react";
import { toast } from "sonner";
import { CampaignPayment } from "../stripe/campaign-payment";
import { PaymentErrorNotification } from "./payment-error-notification";
import { PaymentStatus } from "./payment-status";

interface Campaign {
	id: number;
	name: string;
	total_budget: number | string;
	start_date: string;
	end_date: string;
	status: string;
	failure_reason?: string;
	last_payment_attempt?: string;
}

interface PaymentTransaction {
	id: number;
	amount: number;
	status: string;
	payment_method: string;
	description: string;
	created_at: string;
	failure_reason?: string;
}

interface PaymentRetryInterfaceProps {
	campaign: Campaign;
	paymentHistory: PaymentTransaction[];
}

export function PaymentRetryInterface({ campaign, paymentHistory }: PaymentRetryInterfaceProps) {
	const router = useRouter();
	const [isRetrying, setIsRetrying] = useState(false);
	const [showErrorNotification, setShowErrorNotification] = useState(false);
	const [retryAttempts, setRetryAttempts] = useState(0);

	const isPaymentFailed = ["payment_failed", "payment_error"].includes(campaign.status);
	const isApproved = campaign.status === "approved";
	const maxRetryAttempts = 3;

	useEffect(() => {
		// Show error notification for failed payments
		if (isPaymentFailed) {
			setShowErrorNotification(true);
		}
	}, [isPaymentFailed]);

	const handleRetryPayment = async () => {
		if (retryAttempts >= maxRetryAttempts) {
			toast.error("Maximum retry attempts reached. Please contact support.");
			return;
		}

		setIsRetrying(true);
		try {
			const response = await fetch(`/api/billing/campaigns/${campaign.id}/retry-payment`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.message || "Failed to retry payment");
			}

			const result = await response.json();

			if (result.success) {
				toast.success("Payment retry initiated successfully");
				setRetryAttempts((prev) => prev + 1);
				// Refresh the page to show updated status
				router.refresh();
			} else {
				throw new Error(result.message || "Failed to retry payment");
			}
		} catch (error) {
			console.error("Error retrying payment:", error);
			toast.error(error instanceof Error ? error.message : "Failed to retry payment");
		} finally {
			setIsRetrying(false);
		}
	};

	const handleContactSupport = () => {
		const subject = encodeURIComponent(`Payment Issue - Campaign: ${campaign.name} (${campaign.id})`);
		const body = encodeURIComponent(`
Hello BTDash Support,

I'm experiencing a payment issue with my campaign:

Campaign ID: ${campaign.id}
Campaign Name: ${campaign.name}
Campaign Status: ${campaign.status}
Payment Amount: $${
			typeof campaign.total_budget === "number"
				? campaign.total_budget.toFixed(2)
				: parseFloat(campaign.total_budget?.toString() || "0").toFixed(2)
		}
${campaign.failure_reason ? `Failure Reason: ${campaign.failure_reason}` : ""}
${campaign.last_payment_attempt ? `Last Attempt: ${campaign.last_payment_attempt}` : ""}

Please help me resolve this issue.

Thank you,
    `);

		window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`, "_blank");
	};

	const getStatusBadge = (status: string) => {
		switch (status) {
			case "approved":
				return <Badge variant="secondary">Payment Required</Badge>;
			case "payment_failed":
				return <Badge variant="destructive">Payment Failed</Badge>;
			case "payment_error":
				return <Badge variant="destructive">Payment Error</Badge>;
			case "active":
				return <Badge variant="default">Active</Badge>;
			default:
				return <Badge variant="outline">{status}</Badge>;
		}
	};

	return (
		<div className="space-y-6">
			{/* Error Notification */}
			{showErrorNotification && isPaymentFailed && (
				<PaymentErrorNotification
					campaignId={campaign.id.toString()}
					campaignName={campaign.name}
					errorType={campaign.status as "payment_failed" | "payment_error"}
					errorMessage={campaign.failure_reason || "Payment processing failed"}
					onDismiss={() => setShowErrorNotification(false)}
					onRetry={handleRetryPayment}
				/>
			)}

			<Tabs defaultValue="payment" className="space-y-4">
				<TabsList>
					<TabsTrigger value="payment">Payment</TabsTrigger>
					<TabsTrigger value="history">Payment History</TabsTrigger>
					<TabsTrigger value="details">Campaign Details</TabsTrigger>
				</TabsList>

				<TabsContent value="payment" className="space-y-4">
					{/* Payment Status Card */}
					<PaymentStatus
						campaignId={campaign.id.toString()}
						status={campaign.status as any}
						paymentAmount={
							typeof campaign.total_budget === "number"
								? campaign.total_budget
								: parseFloat(campaign.total_budget?.toString() || "0")
						}
						failureReason={campaign.failure_reason}
						lastPaymentAttempt={campaign.last_payment_attempt}
						onRetryPayment={handleRetryPayment}
						onContactSupport={handleContactSupport}
					/>

					{/* Payment Interface */}
					{(isApproved || isPaymentFailed) && (
						<Card>
							<CardHeader>
								<CardTitle className="flex items-center gap-2">
									<CreditCard className="h-5 w-5" />
									Complete Payment
								</CardTitle>
								<CardDescription>
									{isPaymentFailed
										? "Retry payment to activate your campaign"
										: "Complete payment to activate your campaign"}
								</CardDescription>
							</CardHeader>
							<CardContent>
								{(() => {
									const { StripeProvider } = require("../stripe/stripe-provider");
									return (
										<StripeProvider>
											<CampaignPayment
												campaign={campaign}
												onPaymentSuccess={(paymentIntentId) => {
													toast.success("Payment completed successfully!");
													router.push(`/dashboard/campaigns/${campaign.id}`);
												}}
												onPaymentError={(error) => {
													toast.error(`Payment failed: ${error}`);
													setRetryAttempts((prev) => prev + 1);
												}}
											/>
										</StripeProvider>
									);
								})()}
							</CardContent>
						</Card>
					)}

					{/* Retry Limits Warning */}
					{retryAttempts > 0 && (
						<Alert>
							<Info className="h-4 w-4" />
							<AlertDescription>
								Payment retry attempt {retryAttempts} of {maxRetryAttempts}.
								{retryAttempts >= maxRetryAttempts
									? " Maximum attempts reached. Please contact support for assistance."
									: ` ${maxRetryAttempts - retryAttempts} attempts remaining.`}
							</AlertDescription>
						</Alert>
					)}
				</TabsContent>

				<TabsContent value="history" className="space-y-4">
					<Card>
						<CardHeader>
							<CardTitle className="flex items-center gap-2">
								<History className="h-5 w-5" />
								Payment History
							</CardTitle>
							<CardDescription>All payment attempts for this campaign</CardDescription>
						</CardHeader>
						<CardContent>
							{paymentHistory.length === 0 ? (
								<div className="text-center py-8 text-muted-foreground">
									No payment history available
								</div>
							) : (
								<div className="space-y-4">
									{paymentHistory.map((transaction) => (
										<div
											key={transaction.id}
											className="flex items-center justify-between p-4 border rounded-lg"
										>
											<div className="space-y-1">
												<div className="flex items-center gap-2">
													<span className="font-medium">
														${transaction.amount.toFixed(2)}
													</span>
													{transaction.status === "completed" ? (
														<CheckCircle className="h-4 w-4 text-green-600" />
													) : (
														<XCircle className="h-4 w-4 text-red-600" />
													)}
												</div>
												<div className="text-sm text-muted-foreground">
													{transaction.description}
												</div>
												{transaction.failure_reason && (
													<div className="text-sm text-red-600">
														{transaction.failure_reason}
													</div>
												)}
											</div>
											<div className="text-right">
												<div className="text-sm font-medium">
													{getStatusBadge(transaction.status)}
												</div>
												<div className="text-xs text-muted-foreground">
													{new Date(transaction.created_at).toLocaleString()}
												</div>
											</div>
										</div>
									))}
								</div>
							)}
						</CardContent>
					</Card>
				</TabsContent>

				<TabsContent value="details" className="space-y-4">
					<Card>
						<CardHeader>
							<CardTitle className="flex items-center gap-2">
								<Target className="h-5 w-5" />
								Campaign Details
							</CardTitle>
						</CardHeader>
						<CardContent className="space-y-4">
							<div className="grid grid-cols-2 gap-4">
								<div className="space-y-2">
									<div className="text-sm font-medium">Campaign Name</div>
									<div className="text-sm text-muted-foreground">{campaign.name}</div>
								</div>
								<div className="space-y-2">
									<div className="text-sm font-medium">Status</div>
									<div>{getStatusBadge(campaign.status)}</div>
								</div>
								<div className="space-y-2">
									<div className="text-sm font-medium">Budget</div>
									<div className="text-sm text-muted-foreground">
										$
										{typeof campaign.total_budget === "number"
											? campaign.total_budget.toFixed(2)
											: parseFloat(campaign.total_budget?.toString() || "0").toFixed(2)}
									</div>
								</div>
								<div className="space-y-2">
									<div className="text-sm font-medium">Duration</div>
									<div className="text-sm text-muted-foreground">
										{new Date(campaign.start_date).toLocaleDateString()} -{" "}
										{new Date(campaign.end_date).toLocaleDateString()}
									</div>
								</div>
							</div>
						</CardContent>
					</Card>
				</TabsContent>
			</Tabs>
		</div>
	);
}
