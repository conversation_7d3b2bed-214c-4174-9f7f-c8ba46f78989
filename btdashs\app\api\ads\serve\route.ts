import { AD_SLOTS } from "@/lib/api/ad-serving";
import { NextRequest, NextResponse } from "next/server";

// Handle CORS preflight requests
export async function OPTIONS() {
	return new NextResponse(null, {
		status: 200,
		headers: {
			"Access-Control-Allow-Origin": "*",
			"Access-Control-Allow-Methods": "GET, POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type",
		},
	});
}

export async function POST(request: NextRequest) {
	try {
		console.log("[API] Ad serve request received");

		// Validate request body
		let body;
		try {
			body = await request.json();
			console.log("[API] Request body:", body);
		} catch (parseError) {
			console.error("[API] JSON parse error:", parseError);
			return NextResponse.json({ success: false, message: "Invalid JSON in request body" }, { status: 400 });
		}

		const { slotId, country_code, device_type, language, user_agent } = body;

		// Validate required fields
		if (!slotId) {
			console.error("[API] Missing slotId in request");
			return NextResponse.json({ success: false, message: "Missing required field: slotId" }, { status: 400 });
		}

		// Validate slot ID
		const validSlotIds = Object.values(AD_SLOTS);
		if (!validSlotIds.includes(slotId)) {
			console.error("[API] Invalid slot ID:", slotId, "Valid IDs:", validSlotIds);
			return NextResponse.json(
				{
					success: false,
					message: `Invalid slot ID: ${slotId}. Valid slot IDs: ${validSlotIds.join(", ")}`,
				},
				{ status: 400 }
			);
		}

		console.log("[API] Validated slot ID:", slotId);

		// Validate environment configuration
		if (!process.env.API_BASE_URL && !process.env.APP_BASE_URL) {
			console.error("Missing API_BASE_URL or APP_BASE_URL environment variable");
			return NextResponse.json({ success: false, message: "Server configuration error" }, { status: 500 });
		}

		if (!process.env.INTERNAL_API_KEY) {
			console.error("Missing INTERNAL_API_KEY environment variable");
			return NextResponse.json({ success: false, message: "Server configuration error" }, { status: 500 });
		}

		// Fetch paid ad from backend with timeout
		const API_BASE = process.env.API_BASE_URL || process.env.APP_BASE_URL;
		const params = new URLSearchParams({
			slot: slotId.toString(),
			...(country_code && { country_code }),
			...(device_type && { device_type }),
			...(language && { language }),
			...(user_agent && { user_agent }),
		});

		const headers = new Headers();
		headers.set("x-internal-api-key", process.env.INTERNAL_API_KEY!);

		const paidAd = await Promise.race([
			fetch(`${API_BASE}/serve-ad?${params}`, {
				headers,
				cache: "no-store",
			}).then(async (response) => {
				if (!response.ok) {
					if (response.status === 404) {
						return null; // No ads available
					}
					throw new Error(`Ad serving failed: ${response.status}`);
				}
				const data = await response.json();
				return data.success ? data.data : null;
			}),
			new Promise<null>((_, reject) => setTimeout(() => reject(new Error("Request timeout")), 10000)),
		]);

		if (paidAd) {
			return NextResponse.json({
				success: true,
				data: paidAd,
				message: "Paid ad served successfully",
			});
		} else {
			return NextResponse.json({ success: false, message: "No paid ads available" }, { status: 404 });
		}
	} catch (error) {
		console.error("Error in ad serving route:", error);

		// Provide more specific error messages
		if (error instanceof Error) {
			if (error.message.includes("timeout")) {
				return NextResponse.json(
					{ success: false, message: "Request timeout - backend service unavailable" },
					{ status: 504 }
				);
			}
			if (error.message.includes("ECONNREFUSED")) {
				return NextResponse.json({ success: false, message: "Backend service unavailable" }, { status: 503 });
			}
		}

		return NextResponse.json({ success: false, message: "Internal server error" }, { status: 500 });
	}
}
