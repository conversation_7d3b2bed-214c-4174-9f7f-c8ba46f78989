"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/subnets/[id]/page",{

/***/ "(app-pages-browser)/./components/subnets/subnet-profile.tsx":
/*!***********************************************!*\
  !*** ./components/subnets/subnet-profile.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SubnetProfile)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Book,Github,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book.js\");\n/* harmony import */ var _barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Book,Github,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Book,Github,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var swiper_modules__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! swiper/modules */ \"(app-pages-browser)/./node_modules/swiper/modules/index.mjs\");\n/* harmony import */ var swiper_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! swiper/react */ \"(app-pages-browser)/./node_modules/swiper/swiper-react.mjs\");\n/* harmony import */ var _components_category_tag__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/category-tag */ \"(app-pages-browser)/./components/category-tag.tsx\");\n/* harmony import */ var _components_subnet_documentation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/subnet-documentation */ \"(app-pages-browser)/./components/subnet-documentation.tsx\");\n/* harmony import */ var _components_subnet_github_contribution_graph__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/subnet-github-contribution-graph */ \"(app-pages-browser)/./components/subnet-github-contribution-graph.tsx\");\n/* harmony import */ var _components_subnet_news__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/subnet-news */ \"(app-pages-browser)/./components/subnet-news.tsx\");\n/* harmony import */ var _components_subnet_team__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/subnet-team */ \"(app-pages-browser)/./components/subnet-team.tsx\");\n/* harmony import */ var _components_subnet_validators__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/subnet-validators */ \"(app-pages-browser)/./components/subnet-validators.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _public_tao_logo_svg__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/public/tao-logo.svg */ \"(app-pages-browser)/./public/tao-logo.svg\");\n/* harmony import */ var _components_ads_placements_smart_ad_banner__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ads-placements/smart-ad-banner */ \"(app-pages-browser)/./components/ads-placements/smart-ad-banner.tsx\");\n/* harmony import */ var _components_subnets_subnet_relationship_chart__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/subnets/subnet-relationship-chart */ \"(app-pages-browser)/./components/subnets/subnet-relationship-chart.tsx\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var _subnet_applications__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../subnet-applications */ \"(app-pages-browser)/./components/subnet-applications.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n/* import { useRef, useState } from \"react\"; */ \n\n\n\n\n\n\n\n\n\n\n\n// Import the new chart component\n\n\n\n\nfunction SubnetProfile(param) {\n    let { subnet, metrics, categories, news, products, jobs, events, companies } = param;\n    var _subnet_subnet_ids, _subnet_images, _metrics_github_contributions;\n    /* const [imageError, setImageError] = useState(false);\r\n  const videoRef = useRef<HTMLVideoElement>(null);\r\n  const [isPlaying, setIsPlaying] = useState(false);\r\n  const [isMuted, setIsMuted] = useState(false); */ const netuid = subnet.netuid;\n    var _companies_length, _products_length, _events_length, _jobs_length, _categories_length, _metrics_validators_count, _news_length, _subnet_subnet_ids_length;\n    /* const images = subnet.images?.length\r\n    ? subnet.images\r\n    : [\r\n        \"https://via.placeholder.com/800x400?text=Image+1\",\r\n        \"https://via.placeholder.com/800x400?text=Image+2\",\r\n        \"https://via.placeholder.com/800x400?text=Image+3\",\r\n      ]; */ const data = {\n        companyCount: (_companies_length = companies === null || companies === void 0 ? void 0 : companies.length) !== null && _companies_length !== void 0 ? _companies_length : 0,\n        productCount: (_products_length = products === null || products === void 0 ? void 0 : products.length) !== null && _products_length !== void 0 ? _products_length : 0,\n        eventCount: (_events_length = events === null || events === void 0 ? void 0 : events.length) !== null && _events_length !== void 0 ? _events_length : 0,\n        jobCount: (_jobs_length = jobs === null || jobs === void 0 ? void 0 : jobs.length) !== null && _jobs_length !== void 0 ? _jobs_length : 0,\n        categoryCount: (_categories_length = categories === null || categories === void 0 ? void 0 : categories.length) !== null && _categories_length !== void 0 ? _categories_length : 0,\n        validatorCount: (_metrics_validators_count = metrics === null || metrics === void 0 ? void 0 : metrics.validators_count) !== null && _metrics_validators_count !== void 0 ? _metrics_validators_count : 0,\n        newsCount: (_news_length = news.length) !== null && _news_length !== void 0 ? _news_length : 0,\n        subnetCount: (_subnet_subnet_ids_length = subnet === null || subnet === void 0 ? void 0 : (_subnet_subnet_ids = subnet.subnet_ids) === null || _subnet_subnet_ids === void 0 ? void 0 : _subnet_subnet_ids.length) !== null && _subnet_subnet_ids_length !== void 0 ? _subnet_subnet_ids_length : 0\n    };\n    var _metrics_validators_count1, _metrics_emission;\n    const metricsCards = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardTitle, {\n                            children: \"Price\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-3xl font-bold\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    src: _public_tao_logo_svg__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                                    alt: \"TAO\",\n                                    width: 24,\n                                    height: 24,\n                                    className: \"inline-block pr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 7\n                                }, this),\n                                (metrics === null || metrics === void 0 ? void 0 : metrics.alpha_price_tao) != null && !isNaN(metrics.alpha_price_tao) ? Number(metrics.alpha_price_tao) < 0.01 ? Number(metrics.alpha_price_tao).toFixed(3) : Number(metrics.alpha_price_tao).toFixed(2) : \"0.00\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                lineNumber: 79,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardTitle, {\n                            children: \"Validators\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-3xl font-bold\",\n                            children: (_metrics_validators_count1 = metrics === null || metrics === void 0 ? void 0 : metrics.validators_count) !== null && _metrics_validators_count1 !== void 0 ? _metrics_validators_count1 : 0\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                lineNumber: 95,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardTitle, {\n                            children: \"Emission\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-3xl font-bold\",\n                            children: [\n                                (((_metrics_emission = metrics === null || metrics === void 0 ? void 0 : metrics.emission) !== null && _metrics_emission !== void 0 ? _metrics_emission : 0) / 1e7).toFixed(2),\n                                \"%\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                lineNumber: 104,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardTitle, {\n                            children: \"Miners\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-3xl font-bold \".concat(subnet.active_miners <= 5 ? \"text-red-500\" : subnet.active_miners <= 15 ? \"text-orange-500\" : \"text-green-500\"),\n                            children: subnet.active_miners || 0\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                lineNumber: 113,\n                columnNumber: 4\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n        lineNumber: 78,\n        columnNumber: 3\n    }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-8 px-6 sm:px-8 lg:px-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full mb-8\",\n                    style: {\n                        minHeight: \"90px\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ads_placements_smart_ad_banner__WEBPACK_IMPORTED_MODULE_14__.SmartAdBanner, {\n                        slotId: 7,\n                        googleAdSlot: \"3064454441\",\n                        className: \"mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 6\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 5\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8 grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 rounded-full bg-primary flex items-center justify-center text-black text-3xl font-bold\",\n                                            children: subnet.subnet_symbol || subnet.name.charAt(0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl font-bold\",\n                                                    children: subnet.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 9\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: [\n                                                        \"Subnet ID: \",\n                                                        netuid\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 9\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: [\n                                                        \"Coldkey:\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"https://taostats.io/account/\".concat(subnet.sub_address_pkey, \"/?utm_source=dynamictoamarketcap&utm_medium=referral&utm_campaign=subnet_profile\"),\n                                                            target: \"_blank\",\n                                                            rel: \"noopener noreferrer\",\n                                                            className: \"text-xs text-muted-foreground underline hover:text-primary\",\n                                                            children: subnet.sub_address_pkey\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 10\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 9\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-3 flex flex-wrap gap-2 text-muted-foreground\",\n                                                    children: categories && categories.length > 0 ? categories.map((category, id)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_category_tag__WEBPACK_IMPORTED_MODULE_4__.CategoryTag, {\n                                                            category: category.name\n                                                        }, id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 13\n                                                        }, this)) : null\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 9\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 8\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"my-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_17__.Markdown, {\n                                        children: subnet.description_short\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 8\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-4 mb-8\",\n                                    children: subnet.white_paper ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                        asChild: true,\n                                        size: \"sm\",\n                                        className: \"gap-2\",\n                                        variant: \"default\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: subnet.white_paper,\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 11\n                                                }, this),\n                                                \"Read White Paper\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 10\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 9\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                        size: \"sm\",\n                                        className: \"gap-2\",\n                                        disabled: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 10\n                                            }, this),\n                                            \"White Paper Unavailable\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 9\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 7\n                                }, this),\n                                subnet.images || subnet.main_video_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2 overflow-hidden\",\n                                    children: metricsCards\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 8\n                                }, this) : null\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 6\n                        }, this),\n                        subnet.main_video_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative pb-[56.25%] h-0 overflow-hidden rounded-lg shadow-lg border border-slate-200 dark:border-slate-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                                        className: \"absolute left-0 w-full h-full\",\n                                        src: subnet.main_video_url,\n                                        title: \"Subnet video\",\n                                        frameBorder: \"0\",\n                                        allow: \"autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\n                                        allowFullScreen: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 9\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 8\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-4 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: subnet.website_perm || \"https://subnet-website.example.com\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-12 h-12 rounded-full bg-blue-50 dark:bg-blue-900/30 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-6 w-6 text-blue-600 dark:text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-grow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-slate-900 dark:text-slate-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors\",\n                                                            children: \"Official Website\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 222,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-500 dark:text-slate-400 truncate\",\n                                                            children: subnet.website_perm ? \"\".concat(subnet.website_perm.replace(\"https://www.\", \"\").slice(0, 30)).concat(subnet.website_perm.replace(\"https://www.\", \"\").length > 40 ? \"...\" : \"\") : \"subnet-website.example.com\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 9\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: subnet.github_repo || \"https://github.com/example/subnet-repo\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-12 h-12 rounded-full bg-purple-50 dark:bg-purple-900/30 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-6 w-6 text-purple-600 dark:text-purple-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-grow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-slate-900 dark:text-slate-100 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors\",\n                                                            children: \"GitHub Repository\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 247,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-500 dark:text-slate-400 truncate\",\n                                                            children: subnet.github_repo ? \"\".concat(subnet.github_repo.replace(\"https://github.com/\", \"\").slice(0, 30)).concat(subnet.github_repo.replace(\"https://github.com/\", \"\").length > 40 ? \"...\" : \"\") : \"github.com/example/subnet-repo\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 250,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 9\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 8\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 7\n                        }, this) : !subnet.main_video_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                subnet.images ? null : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"gap-2 overflow-hidden\",\n                                    children: metricsCards\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 32\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"rounded-lg overflow-hidden shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_3__.Swiper, {\n                                        modules: [\n                                            swiper_modules__WEBPACK_IMPORTED_MODULE_2__.Navigation,\n                                            swiper_modules__WEBPACK_IMPORTED_MODULE_2__.Pagination\n                                        ],\n                                        navigation: true,\n                                        pagination: {\n                                            clickable: true\n                                        },\n                                        spaceBetween: 10,\n                                        slidesPerView: 1,\n                                        className: \"w-full h-full\",\n                                        children: (_subnet_images = subnet.images) === null || _subnet_images === void 0 ? void 0 : _subnet_images.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_3__.SwiperSlide, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: image,\n                                                    alt: \"Subnet Image \".concat(index + 1),\n                                                    width: 800,\n                                                    height: 400,\n                                                    className: \"w-full h-auto object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 12\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 11\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 9\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 8\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-4 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: subnet.website_perm || \"https://subnet-website.example.com\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-12 h-12 rounded-full bg-blue-50 dark:bg-blue-900/30 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-6 w-6 text-blue-600 dark:text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-grow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-slate-900 dark:text-slate-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors\",\n                                                            children: \"Official Website\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-500 dark:text-slate-400 truncate\",\n                                                            children: subnet.website_perm ? \"\".concat(subnet.website_perm.replace(\"https://www.\", \"\").slice(0, 30)).concat(subnet.website_perm.replace(\"https://www.\", \"\").length > 40 ? \"...\" : \"\") : \"subnet-website.example.com\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 305,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 9\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: subnet.github_repo || \"https://github.com/example/subnet-repo\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-12 h-12 rounded-full bg-purple-50 dark:bg-purple-900/30 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-6 w-6 text-purple-600 dark:text-purple-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-grow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-slate-900 dark:text-slate-100 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors\",\n                                                            children: \"GitHub Repository\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-500 dark:text-slate-400 truncate\",\n                                                            children: subnet.github_repo ? \"\".concat(subnet.github_repo.replace(\"https://github.com/\", \"\").slice(0, 30)).concat(subnet.github_repo.replace(\"https://github.com/\", \"\").length > 40 ? \"...\" : \"\") : \"github.com/example/subnet-repo\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 330,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 9\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 8\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 7\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \" gap-4 overflow-hidden max-[60px]\",\n                                    children: metricsCards\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 8\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-4 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: subnet.website_perm || \"https://subnet-website.example.com\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-12 h-12 rounded-full bg-blue-50 dark:bg-blue-900/30 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-6 w-6 text-blue-600 dark:text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-grow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-slate-900 dark:text-slate-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors\",\n                                                            children: \"Official Website\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-500 dark:text-slate-400 truncate\",\n                                                            children: subnet.website_perm ? \"\".concat(subnet.website_perm.replace(\"https://www.\", \"\").slice(0, 30)).concat(subnet.website_perm.replace(\"https://www.\", \"\").length > 40 ? \"...\" : \"\") : \"subnet-website.example.com\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 9\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: subnet.github_repo || \"https://github.com/example/subnet-repo\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-12 h-12 rounded-full bg-purple-50 dark:bg-purple-900/30 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-6 w-6 text-purple-600 dark:text-purple-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-grow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-slate-900 dark:text-slate-100 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors\",\n                                                            children: \"GitHub Repository\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 385,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-500 dark:text-slate-400 truncate\",\n                                                            children: subnet.github_repo ? \"\".concat(subnet.github_repo.replace(\"https://github.com/\", \"\").slice(0, 30)).concat(subnet.github_repo.replace(\"https://github.com/\", \"\").length > 40 ? \"...\" : \"\") : \"github.com/example/subnet-repo\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 9\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 8\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 7\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ads_placements_smart_ad_banner__WEBPACK_IMPORTED_MODULE_14__.SmartAdBanner, {\n                                slotId: 9,\n                                googleAdSlot: \"9765594162\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 7\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 407,\n                            columnNumber: 6\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 5\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-7 gap-4 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-span-5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_subnet_github_contribution_graph__WEBPACK_IMPORTED_MODULE_6__.SubnetGithubContributionGraph, {\n                                className: \"h-[360px]\",\n                                contributions: (metrics === null || metrics === void 0 ? void 0 : (_metrics_github_contributions = metrics.github_contributions) === null || _metrics_github_contributions === void 0 ? void 0 : _metrics_github_contributions.data) || []\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                lineNumber: 416,\n                                columnNumber: 7\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 415,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-[360px] col-span-2 overflow-visible\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_subnets_subnet_relationship_chart__WEBPACK_IMPORTED_MODULE_15__.SubnetRelationshipChart, {\n                                subnetId: subnet.name,\n                                data: data,\n                                className: \"h-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 7\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 423,\n                            columnNumber: 6\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                    lineNumber: 413,\n                    columnNumber: 5\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-0 mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_subnet_applications__WEBPACK_IMPORTED_MODULE_16__.SubnetApplications, {\n                        products: products\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 430,\n                        columnNumber: 6\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                    lineNumber: 429,\n                    columnNumber: 5\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full mb-8 flex justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ads_placements_smart_ad_banner__WEBPACK_IMPORTED_MODULE_14__.SmartAdBanner, {\n                        slotId: 10,\n                        googleAdSlot: \"SUBNET_HALF_PAGE_PLACEHOLDER\",\n                        className: \"mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 435,\n                        columnNumber: 6\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                    lineNumber: 434,\n                    columnNumber: 5\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.Tabs, {\n                    defaultValue: \"overview\",\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsList, {\n                            className: \"flex flex-wrap\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsTrigger, {\n                                    value: \"overview\",\n                                    children: \"Overview\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsTrigger, {\n                                    value: \"team\",\n                                    children: \"Team\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 442,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsTrigger, {\n                                    value: \"documentation\",\n                                    children: \"Documentation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsTrigger, {\n                                    value: \"validators\",\n                                    children: \"Validators\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 444,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsTrigger, {\n                                    value: \"news\",\n                                    children: \"News\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 445,\n                                    columnNumber: 7\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 440,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-1 gap-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsContent, {\n                                        value: \"overview\",\n                                        className: \"space-y-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardTitle, {\n                                                        className: \"text-lg\",\n                                                        children: \"Key Features\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid md:grid-cols-2 gap-4\",\n                                                        children: subnet.key_features && subnet.key_features.length > 0 ? subnet.key_features[0].map((feature, id)=>feature && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2 pb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium\",\n                                                                        children: feature.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                                        lineNumber: 462,\n                                                                        columnNumber: 17\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-muted-foreground\",\n                                                                        children: feature.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                                        lineNumber: 463,\n                                                                        columnNumber: 17\n                                                                    }, this)\n                                                                ]\n                                                            }, id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 16\n                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-muted-foreground\",\n                                                            children: \"No key features available.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 470,\n                                                            columnNumber: 13\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                        lineNumber: 456,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 9\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 8\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsContent, {\n                                        value: \"team\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_subnet_team__WEBPACK_IMPORTED_MODULE_8__.SubnetTeam, {\n                                            subnet: subnet\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 9\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 477,\n                                        columnNumber: 8\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsContent, {\n                                        value: \"documentation\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_subnet_documentation__WEBPACK_IMPORTED_MODULE_5__.SubnetDocumentation, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 9\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 480,\n                                        columnNumber: 8\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsContent, {\n                                        value: \"validators\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_subnet_validators__WEBPACK_IMPORTED_MODULE_9__.SubnetValidators, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 9\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 8\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsContent, {\n                                        value: \"news\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_subnet_news__WEBPACK_IMPORTED_MODULE_7__.SubnetNews, {\n                                            news: news\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 487,\n                                            columnNumber: 9\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 8\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                lineNumber: 449,\n                                columnNumber: 7\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 448,\n                            columnNumber: 6\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                    lineNumber: 439,\n                    columnNumber: 5\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 mt-8 mb-8 min-h-[90px] w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ads_placements_smart_ad_banner__WEBPACK_IMPORTED_MODULE_14__.SmartAdBanner, {\n                        slotId: 8,\n                        googleAdSlot: \"SUBNET_MED_RECT_PLACEHOLDER\",\n                        className: \"w-full h-full\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 495,\n                        columnNumber: 6\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                    lineNumber: 494,\n                    columnNumber: 5\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n            lineNumber: 136,\n            columnNumber: 4\n        }, this)\n    }, void 0, false);\n}\n_c = SubnetProfile;\nvar _c;\n$RefreshReg$(_c, \"SubnetProfile\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/subnets/subnet-profile.tsx\n"));

/***/ })

});