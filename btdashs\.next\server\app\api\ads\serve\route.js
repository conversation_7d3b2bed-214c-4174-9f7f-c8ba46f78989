/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/ads/serve/route";
exports.ids = ["app/api/ads/serve/route"];
exports.modules = {

/***/ "(rsc)/./app/api/ads/serve/route.ts":
/*!************************************!*\
  !*** ./app/api/ads/serve/route.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api/ad-serving */ \"(rsc)/./lib/api/ad-serving.ts\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n\n// Handle CORS preflight requests\nasync function OPTIONS() {\n    return new next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse(null, {\n        status: 200,\n        headers: {\n            \"Access-Control-Allow-Origin\": \"*\",\n            \"Access-Control-Allow-Methods\": \"GET, POST, OPTIONS\",\n            \"Access-Control-Allow-Headers\": \"Content-Type\"\n        }\n    });\n}\n// Simple GET method for testing\nasync function GET() {\n    return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n        success: true,\n        message: \"Ad serving API is working\",\n        timestamp: new Date().toISOString()\n    });\n}\nasync function POST(request) {\n    try {\n        console.log(\"[API] Ad serve request received\");\n        // Validate request body\n        let body;\n        try {\n            body = await request.json();\n            console.log(\"[API] Request body:\", body);\n        } catch (parseError) {\n            console.error(\"[API] JSON parse error:\", parseError);\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: \"Invalid JSON in request body\"\n            }, {\n                status: 400\n            });\n        }\n        const { slotId, country_code, device_type, language, user_agent } = body;\n        // Validate required fields\n        if (!slotId) {\n            console.error(\"[API] Missing slotId in request\");\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: \"Missing required field: slotId\"\n            }, {\n                status: 400\n            });\n        }\n        // Validate slot ID\n        const validSlotIds = Object.values(_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_0__.AD_SLOTS);\n        if (!validSlotIds.includes(slotId)) {\n            console.error(\"[API] Invalid slot ID:\", slotId, \"Valid IDs:\", validSlotIds);\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: `Invalid slot ID: ${slotId}. Valid slot IDs: ${validSlotIds.join(\", \")}`\n            }, {\n                status: 400\n            });\n        }\n        console.log(\"[API] Validated slot ID:\", slotId);\n        // Validate environment configuration\n        if (!process.env.API_BASE_URL && !process.env.APP_BASE_URL) {\n            console.error(\"Missing API_BASE_URL or APP_BASE_URL environment variable\");\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: \"Server configuration error\"\n            }, {\n                status: 500\n            });\n        }\n        if (!process.env.INTERNAL_API_KEY) {\n            console.error(\"Missing INTERNAL_API_KEY environment variable\");\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: \"Server configuration error\"\n            }, {\n                status: 500\n            });\n        }\n        // Fetch paid ad from backend with timeout\n        const API_BASE = process.env.API_BASE_URL || process.env.APP_BASE_URL;\n        const params = new URLSearchParams({\n            slot: slotId.toString(),\n            ...country_code && {\n                country_code\n            },\n            ...device_type && {\n                device_type\n            },\n            ...language && {\n                language\n            },\n            ...user_agent && {\n                user_agent\n            }\n        });\n        const headers = new Headers();\n        headers.set(\"x-internal-api-key\", process.env.INTERNAL_API_KEY);\n        const paidAd = await Promise.race([\n            fetch(`${API_BASE}/serve-ad?${params}`, {\n                headers,\n                cache: \"no-store\"\n            }).then(async (response)=>{\n                if (!response.ok) {\n                    if (response.status === 404) {\n                        return null; // No ads available\n                    }\n                    throw new Error(`Ad serving failed: ${response.status}`);\n                }\n                const data = await response.json();\n                return data.success ? data.data : null;\n            }),\n            new Promise((_, reject)=>setTimeout(()=>reject(new Error(\"Request timeout\")), 10000))\n        ]);\n        if (paidAd) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: true,\n                data: paidAd,\n                message: \"Paid ad served successfully\"\n            });\n        } else {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: \"No paid ads available\"\n            }, {\n                status: 404\n            });\n        }\n    } catch (error) {\n        console.error(\"Error in ad serving route:\", error);\n        // Provide more specific error messages\n        if (error instanceof Error) {\n            if (error.message.includes(\"timeout\")) {\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                    success: false,\n                    message: \"Request timeout - backend service unavailable\"\n                }, {\n                    status: 504\n                });\n            }\n            if (error.message.includes(\"ECONNREFUSED\")) {\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                    success: false,\n                    message: \"Backend service unavailable\"\n                }, {\n                    status: 503\n                });\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            success: false,\n            message: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/ads/serve/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/api/ad-serving.ts":
/*!*******************************!*\
  !*** ./lib/api/ad-serving.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AD_SLOTS: () => (/* binding */ AD_SLOTS),\n/* harmony export */   GOOGLE_AD_SLOTS: () => (/* binding */ GOOGLE_AD_SLOTS),\n/* harmony export */   SLOT_DIMENSIONS: () => (/* binding */ SLOT_DIMENSIONS),\n/* harmony export */   fetchPaidAd: () => (/* binding */ fetchPaidAd),\n/* harmony export */   generateSessionId: () => (/* binding */ generateSessionId),\n/* harmony export */   getCountryCode: () => (/* binding */ getCountryCode),\n/* harmony export */   getDeviceType: () => (/* binding */ getDeviceType),\n/* harmony export */   getLanguage: () => (/* binding */ getLanguage),\n/* harmony export */   trackAdClick: () => (/* binding */ trackAdClick),\n/* harmony export */   trackAdImpression: () => (/* binding */ trackAdImpression)\n/* harmony export */ });\n// Database ad slot IDs and their configurations\nconst AD_SLOTS = {\n    // Home Page Slots (1-4)\n    HOME_LEADERBOARD: 1,\n    HOME_BILLBOARD: 2,\n    HOME_MEDIUM_RECTANGLE: 3,\n    HOME_SKYSCRAPER: 4,\n    // Subnet Page Slots (7-10)\n    SUBNET_MEDIUM_RECTANGLE: 7,\n    SUBNET_HALF_PAGE: 8,\n    SUBNET_BANNER: 9,\n    SUBNET_WIDE_SKYSCRAPER: 10,\n    // Company Page Slots (11-14)\n    COMPANY_MEDIUM_RECTANGLE: 11,\n    COMPANY_HALF_PAGE: 12,\n    COMPANY_LEADERBOARD: 13,\n    COMPANY_SQUARE_BUTTON: 14,\n    // Global Slots (15-16)\n    GLOBAL_POPUP: 15,\n    GLOBAL_STICKY_FOOTER: 16\n};\n// Slot dimensions mapping\nconst SLOT_DIMENSIONS = {\n    [AD_SLOTS.HOME_LEADERBOARD]: {\n        width: 728,\n        height: 90\n    },\n    [AD_SLOTS.HOME_BILLBOARD]: {\n        width: 970,\n        height: 250\n    },\n    [AD_SLOTS.HOME_MEDIUM_RECTANGLE]: {\n        width: 300,\n        height: 250\n    },\n    [AD_SLOTS.HOME_SKYSCRAPER]: {\n        width: 160,\n        height: 600\n    },\n    [AD_SLOTS.SUBNET_MEDIUM_RECTANGLE]: {\n        width: 300,\n        height: 250\n    },\n    [AD_SLOTS.SUBNET_HALF_PAGE]: {\n        width: 300,\n        height: 600\n    },\n    [AD_SLOTS.SUBNET_BANNER]: {\n        width: 468,\n        height: 60\n    },\n    [AD_SLOTS.SUBNET_WIDE_SKYSCRAPER]: {\n        width: 160,\n        height: 600\n    },\n    [AD_SLOTS.COMPANY_MEDIUM_RECTANGLE]: {\n        width: 300,\n        height: 250\n    },\n    [AD_SLOTS.COMPANY_HALF_PAGE]: {\n        width: 300,\n        height: 600\n    },\n    [AD_SLOTS.COMPANY_LEADERBOARD]: {\n        width: 728,\n        height: 90\n    },\n    [AD_SLOTS.COMPANY_SQUARE_BUTTON]: {\n        width: 125,\n        height: 125\n    },\n    [AD_SLOTS.GLOBAL_POPUP]: {\n        width: 400,\n        height: 400\n    },\n    [AD_SLOTS.GLOBAL_STICKY_FOOTER]: {\n        width: 320,\n        height: 50\n    }\n};\n// Google AdSense slots - each slot has a unique ID to avoid conflicts\nconst GOOGLE_AD_SLOTS = {\n    [AD_SLOTS.HOME_LEADERBOARD]: \"3369057847\",\n    [AD_SLOTS.HOME_BILLBOARD]: \"6182923442\",\n    [AD_SLOTS.HOME_MEDIUM_RECTANGLE]: \"7844510005\",\n    [AD_SLOTS.HOME_SKYSCRAPER]: \"5254383953\",\n    [AD_SLOTS.SUBNET_MEDIUM_RECTANGLE]: \"3064454441\",\n    [AD_SLOTS.SUBNET_HALF_PAGE]: \"9249469283\",\n    [AD_SLOTS.SUBNET_BANNER]: \"9765594162\",\n    [AD_SLOTS.SUBNET_WIDE_SKYSCRAPER]: \"6623305948\",\n    [AD_SLOTS.COMPANY_MEDIUM_RECTANGLE]: \"3997142602\",\n    [AD_SLOTS.COMPANY_HALF_PAGE]: \"8621384528\",\n    [AD_SLOTS.COMPANY_LEADERBOARD]: \"7308302852\",\n    [AD_SLOTS.COMPANY_SQUARE_BUTTON]: \"2378058731\",\n    [AD_SLOTS.GLOBAL_POPUP]: \"5445955649\",\n    [AD_SLOTS.GLOBAL_STICKY_FOOTER]: \"1064977062\"\n};\n/**\n * SERVER-SIDE ONLY\n * Fetch a paid ad from the backend API\n */ async function fetchPaidAd(slotId, userContext) {\n    try {\n        const API_BASE = process.env.API_BASE_URL || process.env.APP_BASE_URL;\n        if (!API_BASE) {\n            console.error(\"API_BASE_URL not configured\");\n            return null;\n        }\n        const params = new URLSearchParams({\n            slot: slotId.toString(),\n            ...userContext?.country_code && {\n                country_code: userContext.country_code\n            },\n            ...userContext?.device_type && {\n                device_type: userContext.device_type\n            },\n            ...userContext?.language && {\n                language: userContext.language\n            },\n            ...userContext?.user_agent && {\n                user_agent: userContext.user_agent\n            }\n        });\n        const headers = new Headers();\n        headers.set(\"x-internal-api-key\", process.env.INTERNAL_API_KEY);\n        const response = await fetch(`${API_BASE}/serve-ad?${params}`, {\n            headers,\n            cache: \"no-store\"\n        });\n        if (!response.ok) {\n            if (response.status === 404) {\n                // No ads available for this slot\n                return null;\n            }\n            throw new Error(`Ad serving failed: ${response.status}`);\n        }\n        const result = await response.json();\n        if (result.success && result.data) {\n            return result.data;\n        }\n        return null;\n    } catch (error) {\n        console.error(\"Error fetching paid ad:\", error);\n        return null;\n    }\n}\n/**\n * CLIENT-SIDE\n * Track ad impression\n */ async function trackAdImpression(impressionData) {\n    try {\n        const response = await fetch(\"/api/ads/track/impression\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(impressionData)\n        });\n        return response.ok;\n    } catch (error) {\n        console.error(\"Error tracking impression:\", error);\n        return false;\n    }\n}\n/**\n * CLIENT-SIDE\n * Track ad click and redirect\n */ async function trackAdClick(clickData) {\n    try {\n        const response = await fetch(\"/api/ads/track/click\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(clickData)\n        });\n        return response.ok;\n    } catch (error) {\n        console.error(\"Error tracking click:\", error);\n        return false;\n    }\n}\n/**\n * Generate a session ID for ad tracking\n */ function generateSessionId() {\n    // Generate a UUID v4 compatible string\n    return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(/[xy]/g, function(c) {\n        const r = Math.random() * 16 | 0;\n        const v = c == \"x\" ? r : r & 0x3 | 0x8;\n        return v.toString(16);\n    });\n}\n/**\n * Get device type from user agent\n */ function getDeviceType(userAgent) {\n    if (!userAgent) return \"unknown\";\n    const ua = userAgent.toLowerCase();\n    if (ua.includes(\"mobile\") || ua.includes(\"android\") || ua.includes(\"iphone\")) {\n        return \"mobile\";\n    }\n    if (ua.includes(\"tablet\") || ua.includes(\"ipad\")) {\n        return \"tablet\";\n    }\n    return \"desktop\";\n}\n/**\n * Get user's country code (placeholder - would integrate with geolocation service)\n */ function getCountryCode() {\n    // In a real implementation, this would use a geolocation service\n    // For now, return a default\n    return \"US\";\n}\n/**\n * Get user's language preference\n */ function getLanguage() {\n    if (false) {}\n    return \"en-US\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/api/ad-serving.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fads%2Fserve%2Froute&page=%2Fapi%2Fads%2Fserve%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fads%2Fserve%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fads%2Fserve%2Froute&page=%2Fapi%2Fads%2Fserve%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fads%2Fserve%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_benji_Desktop_G_PROG_Nicolas_btdash_ecosystem_btdashs_app_api_ads_serve_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/ads/serve/route.ts */ \"(rsc)/./app/api/ads/serve/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/ads/serve/route\",\n        pathname: \"/api/ads/serve\",\n        filename: \"route\",\n        bundlePath: \"app/api/ads/serve/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\api\\\\ads\\\\serve\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_benji_Desktop_G_PROG_Nicolas_btdash_ecosystem_btdashs_app_api_ads_serve_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fads%2Fserve%2Froute&page=%2Fapi%2Fads%2Fserve%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fads%2Fserve%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fads%2Fserve%2Froute&page=%2Fapi%2Fads%2Fserve%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fads%2Fserve%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();