/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/test-backend/route";
exports.ids = ["app/api/test-backend/route"];
exports.modules = {

/***/ "(rsc)/./app/api/test-backend/route.ts":
/*!***************************************!*\
  !*** ./app/api/test-backend/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nasync function GET(request) {\n    try {\n        const API_BASE = process.env.API_BASE_URL || process.env.APP_BASE_URL;\n        const INTERNAL_API_KEY = process.env.INTERNAL_API_KEY;\n        console.log(\"Testing backend connection...\");\n        console.log(\"API_BASE:\", API_BASE);\n        console.log(\"INTERNAL_API_KEY:\", INTERNAL_API_KEY ? \"Set\" : \"Not set\");\n        if (!API_BASE) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"API_BASE_URL not configured\",\n                env: {\n                    API_BASE_URL: process.env.API_BASE_URL,\n                    APP_BASE_URL: process.env.APP_BASE_URL\n                }\n            }, {\n                status: 500\n            });\n        }\n        if (!INTERNAL_API_KEY) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: \"INTERNAL_API_KEY not configured\"\n            }, {\n                status: 500\n            });\n        }\n        const headers = new Headers();\n        headers.set(\"x-internal-api-key\", INTERNAL_API_KEY);\n        const response = await fetch(`${API_BASE}/serve-ad?slot=1`, {\n            headers,\n            cache: \"no-store\"\n        });\n        const responseData = await response.text();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            backend_status: response.status,\n            backend_response: responseData,\n            backend_headers: Object.fromEntries(response.headers.entries()),\n            config: {\n                API_BASE,\n                has_internal_key: !!INTERNAL_API_KEY\n            }\n        });\n    } catch (error) {\n        console.error(\"Backend test error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error instanceof Error ? error.message : \"Unknown error\",\n            stack: error instanceof Error ? error.stack : undefined\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvYXBpL3Rlc3QtYmFja2VuZC9yb3V0ZS50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUF3RDtBQUVqRCxlQUFlQyxJQUFJQyxPQUFvQjtJQUM3QyxJQUFJO1FBQ0gsTUFBTUMsV0FBV0MsUUFBUUMsR0FBRyxDQUFDQyxZQUFZLElBQUlGLFFBQVFDLEdBQUcsQ0FBQ0UsWUFBWTtRQUNyRSxNQUFNQyxtQkFBbUJKLFFBQVFDLEdBQUcsQ0FBQ0csZ0JBQWdCO1FBRXJEQyxRQUFRQyxHQUFHLENBQUM7UUFDWkQsUUFBUUMsR0FBRyxDQUFDLGFBQWFQO1FBQ3pCTSxRQUFRQyxHQUFHLENBQUMscUJBQXFCRixtQkFBbUIsUUFBUTtRQUU1RCxJQUFJLENBQUNMLFVBQVU7WUFDZCxPQUFPSCxxREFBWUEsQ0FBQ1csSUFBSSxDQUFDO2dCQUN4QkMsU0FBUztnQkFDVEMsT0FBTztnQkFDUFIsS0FBSztvQkFDSkMsY0FBY0YsUUFBUUMsR0FBRyxDQUFDQyxZQUFZO29CQUN0Q0MsY0FBY0gsUUFBUUMsR0FBRyxDQUFDRSxZQUFZO2dCQUN2QztZQUNELEdBQUc7Z0JBQUVPLFFBQVE7WUFBSTtRQUNsQjtRQUVBLElBQUksQ0FBQ04sa0JBQWtCO1lBQ3RCLE9BQU9SLHFEQUFZQSxDQUFDVyxJQUFJLENBQUM7Z0JBQ3hCQyxTQUFTO2dCQUNUQyxPQUFPO1lBQ1IsR0FBRztnQkFBRUMsUUFBUTtZQUFJO1FBQ2xCO1FBRUEsTUFBTUMsVUFBVSxJQUFJQztRQUNwQkQsUUFBUUUsR0FBRyxDQUFDLHNCQUFzQlQ7UUFFbEMsTUFBTVUsV0FBVyxNQUFNQyxNQUFNLEdBQUdoQixTQUFTLGdCQUFnQixDQUFDLEVBQUU7WUFDM0RZO1lBQ0FLLE9BQU87UUFDUjtRQUVBLE1BQU1DLGVBQWUsTUFBTUgsU0FBU0ksSUFBSTtRQUV4QyxPQUFPdEIscURBQVlBLENBQUNXLElBQUksQ0FBQztZQUN4QkMsU0FBUztZQUNUVyxnQkFBZ0JMLFNBQVNKLE1BQU07WUFDL0JVLGtCQUFrQkg7WUFDbEJJLGlCQUFpQkMsT0FBT0MsV0FBVyxDQUFDVCxTQUFTSCxPQUFPLENBQUNhLE9BQU87WUFDNURDLFFBQVE7Z0JBQ1AxQjtnQkFDQTJCLGtCQUFrQixDQUFDLENBQUN0QjtZQUNyQjtRQUNEO0lBRUQsRUFBRSxPQUFPSyxPQUFPO1FBQ2ZKLFFBQVFJLEtBQUssQ0FBQyx1QkFBdUJBO1FBQ3JDLE9BQU9iLHFEQUFZQSxDQUFDVyxJQUFJLENBQUM7WUFDeEJDLFNBQVM7WUFDVEMsT0FBT0EsaUJBQWlCa0IsUUFBUWxCLE1BQU1tQixPQUFPLEdBQUc7WUFDaERDLE9BQU9wQixpQkFBaUJrQixRQUFRbEIsTUFBTW9CLEtBQUssR0FBR0M7UUFDL0MsR0FBRztZQUFFcEIsUUFBUTtRQUFJO0lBQ2xCO0FBQ0QiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmVuamlcXERlc2t0b3BcXEdfUFJPR1xcTmljb2xhc1xcYnRkYXNoLWVjb3N5c3RlbVxcYnRkYXNoc1xcYXBwXFxhcGlcXHRlc3QtYmFja2VuZFxccm91dGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dFJlcXVlc3QsIE5leHRSZXNwb25zZSB9IGZyb20gXCJuZXh0L3NlcnZlclwiO1xuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gR0VUKHJlcXVlc3Q6IE5leHRSZXF1ZXN0KSB7XG5cdHRyeSB7XG5cdFx0Y29uc3QgQVBJX0JBU0UgPSBwcm9jZXNzLmVudi5BUElfQkFTRV9VUkwgfHwgcHJvY2Vzcy5lbnYuQVBQX0JBU0VfVVJMO1xuXHRcdGNvbnN0IElOVEVSTkFMX0FQSV9LRVkgPSBwcm9jZXNzLmVudi5JTlRFUk5BTF9BUElfS0VZO1xuXG5cdFx0Y29uc29sZS5sb2coXCJUZXN0aW5nIGJhY2tlbmQgY29ubmVjdGlvbi4uLlwiKTtcblx0XHRjb25zb2xlLmxvZyhcIkFQSV9CQVNFOlwiLCBBUElfQkFTRSk7XG5cdFx0Y29uc29sZS5sb2coXCJJTlRFUk5BTF9BUElfS0VZOlwiLCBJTlRFUk5BTF9BUElfS0VZID8gXCJTZXRcIiA6IFwiTm90IHNldFwiKTtcblxuXHRcdGlmICghQVBJX0JBU0UpIHtcblx0XHRcdHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IFxuXHRcdFx0XHRzdWNjZXNzOiBmYWxzZSwgXG5cdFx0XHRcdGVycm9yOiBcIkFQSV9CQVNFX1VSTCBub3QgY29uZmlndXJlZFwiLFxuXHRcdFx0XHRlbnY6IHtcblx0XHRcdFx0XHRBUElfQkFTRV9VUkw6IHByb2Nlc3MuZW52LkFQSV9CQVNFX1VSTCxcblx0XHRcdFx0XHRBUFBfQkFTRV9VUkw6IHByb2Nlc3MuZW52LkFQUF9CQVNFX1VSTFxuXHRcdFx0XHR9XG5cdFx0XHR9LCB7IHN0YXR1czogNTAwIH0pO1xuXHRcdH1cblxuXHRcdGlmICghSU5URVJOQUxfQVBJX0tFWSkge1xuXHRcdFx0cmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgXG5cdFx0XHRcdHN1Y2Nlc3M6IGZhbHNlLCBcblx0XHRcdFx0ZXJyb3I6IFwiSU5URVJOQUxfQVBJX0tFWSBub3QgY29uZmlndXJlZFwiIFxuXHRcdFx0fSwgeyBzdGF0dXM6IDUwMCB9KTtcblx0XHR9XG5cblx0XHRjb25zdCBoZWFkZXJzID0gbmV3IEhlYWRlcnMoKTtcblx0XHRoZWFkZXJzLnNldChcIngtaW50ZXJuYWwtYXBpLWtleVwiLCBJTlRFUk5BTF9BUElfS0VZKTtcblxuXHRcdGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7QVBJX0JBU0V9L3NlcnZlLWFkP3Nsb3Q9MWAsIHtcblx0XHRcdGhlYWRlcnMsXG5cdFx0XHRjYWNoZTogXCJuby1zdG9yZVwiLFxuXHRcdH0pO1xuXG5cdFx0Y29uc3QgcmVzcG9uc2VEYXRhID0gYXdhaXQgcmVzcG9uc2UudGV4dCgpO1xuXG5cdFx0cmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcblx0XHRcdHN1Y2Nlc3M6IHRydWUsXG5cdFx0XHRiYWNrZW5kX3N0YXR1czogcmVzcG9uc2Uuc3RhdHVzLFxuXHRcdFx0YmFja2VuZF9yZXNwb25zZTogcmVzcG9uc2VEYXRhLFxuXHRcdFx0YmFja2VuZF9oZWFkZXJzOiBPYmplY3QuZnJvbUVudHJpZXMocmVzcG9uc2UuaGVhZGVycy5lbnRyaWVzKCkpLFxuXHRcdFx0Y29uZmlnOiB7XG5cdFx0XHRcdEFQSV9CQVNFLFxuXHRcdFx0XHRoYXNfaW50ZXJuYWxfa2V5OiAhIUlOVEVSTkFMX0FQSV9LRVlcblx0XHRcdH1cblx0XHR9KTtcblxuXHR9IGNhdGNoIChlcnJvcikge1xuXHRcdGNvbnNvbGUuZXJyb3IoXCJCYWNrZW5kIHRlc3QgZXJyb3I6XCIsIGVycm9yKTtcblx0XHRyZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuXHRcdFx0c3VjY2VzczogZmFsc2UsXG5cdFx0XHRlcnJvcjogZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiBcIlVua25vd24gZXJyb3JcIixcblx0XHRcdHN0YWNrOiBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3Iuc3RhY2sgOiB1bmRlZmluZWRcblx0XHR9LCB7IHN0YXR1czogNTAwIH0pO1xuXHR9XG59XG4iXSwibmFtZXMiOlsiTmV4dFJlc3BvbnNlIiwiR0VUIiwicmVxdWVzdCIsIkFQSV9CQVNFIiwicHJvY2VzcyIsImVudiIsIkFQSV9CQVNFX1VSTCIsIkFQUF9CQVNFX1VSTCIsIklOVEVSTkFMX0FQSV9LRVkiLCJjb25zb2xlIiwibG9nIiwianNvbiIsInN1Y2Nlc3MiLCJlcnJvciIsInN0YXR1cyIsImhlYWRlcnMiLCJIZWFkZXJzIiwic2V0IiwicmVzcG9uc2UiLCJmZXRjaCIsImNhY2hlIiwicmVzcG9uc2VEYXRhIiwidGV4dCIsImJhY2tlbmRfc3RhdHVzIiwiYmFja2VuZF9yZXNwb25zZSIsImJhY2tlbmRfaGVhZGVycyIsIk9iamVjdCIsImZyb21FbnRyaWVzIiwiZW50cmllcyIsImNvbmZpZyIsImhhc19pbnRlcm5hbF9rZXkiLCJFcnJvciIsIm1lc3NhZ2UiLCJzdGFjayIsInVuZGVmaW5lZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/api/test-backend/route.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftest-backend%2Froute&page=%2Fapi%2Ftest-backend%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftest-backend%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftest-backend%2Froute&page=%2Fapi%2Ftest-backend%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftest-backend%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_benji_Desktop_G_PROG_Nicolas_btdash_ecosystem_btdashs_app_api_test_backend_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/test-backend/route.ts */ \"(rsc)/./app/api/test-backend/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/test-backend/route\",\n        pathname: \"/api/test-backend\",\n        filename: \"route\",\n        bundlePath: \"app/api/test-backend/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\api\\\\test-backend\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_benji_Desktop_G_PROG_Nicolas_btdash_ecosystem_btdashs_app_api_test_backend_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftest-backend%2Froute&page=%2Fapi%2Ftest-backend%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftest-backend%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftest-backend%2Froute&page=%2Fapi%2Ftest-backend%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftest-backend%2Froute.ts&appDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbenji%5CDesktop%5CG_PROG%5CNicolas%5Cbtdash-ecosystem%5Cbtdashs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();