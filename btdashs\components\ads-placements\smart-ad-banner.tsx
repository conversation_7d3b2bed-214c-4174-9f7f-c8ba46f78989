"use client";

import {
	generateSessionId,
	getCountryCode,
	getDeviceType,
	getLanguage,
	SLOT_DIMENSIONS,
	trackAdClick,
	trackAdImpression,
} from "@/lib/ad-serving";
import { ServedAd } from "@/lib/db/models";
import { useEffect, useRef, useState } from "react";

// Simple logging functions for ad performance tracking
const logAdPerformance = (data: any) => {
	if (process.env.NODE_ENV === "development") {
		console.log("Ad Performance:", data);
	}
};

const logAdError = (data: any) => {
	if (process.env.NODE_ENV === "development") {
		console.error("Ad Error:", data);
	}
};

interface SmartAdBannerProps {
	slotId: number; // Database slot ID
	className?: string;
	googleAdSlot: string; // Required unique Google AdSense slot ID
	enablePaidAds?: boolean; // Allow disabling paid ads for testing
}

export function SmartAdBanner({ slotId, className, googleAdSlot, enablePaidAds = true }: SmartAdBannerProps) {
	const [paidAd, setPaidAd] = useState<ServedAd | null>(null);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [retryCount, setRetryCount] = useState(0);
	const [sessionId] = useState(() => generateSessionId());
	const [hasTrackedImpression, setHasTrackedImpression] = useState(false);
	const [googleAdError, setGoogleAdError] = useState<string | null>(null);
	const [fallbackMode, setFallbackMode] = useState<"none" | "google" | "placeholder">("none");
	const [loadStartTime] = useState(() => Date.now());

	const adContainerRef = useRef<HTMLDivElement>(null);
	const googleAdInitialized = useRef(false);
	const googleAdLoadTimeout = useRef<NodeJS.Timeout | null>(null);
	const maxRetries = 3;

	// Get dimensions for this slot
	const dimensions = SLOT_DIMENSIONS[slotId as keyof typeof SLOT_DIMENSIONS];
	if (!dimensions) {
		console.error(`No dimensions found for slot ID: ${slotId}`);
		return null;
	}

	// Fetch paid ad on component mount with retry logic
	useEffect(() => {
		if (!enablePaidAds) {
			setIsLoading(false);
			return;
		}

		const fetchPaidAd = async (attempt = 0) => {
			try {
				setError(null);

				const userContext = {
					country_code: getCountryCode(),
					device_type: getDeviceType(navigator.userAgent),
					language: getLanguage(),
					user_agent: navigator.userAgent,
				};

				console.log(`[Ad Serving] Fetching paid ad for slot ${slotId}`, userContext);

				const controller = new AbortController();
				const timeoutId = setTimeout(() => {
					console.log(`[Ad Serving] Request timeout for slot ${slotId}`);
					controller.abort();
				}, 10000); // Increased to 10 second timeout

				const response = await fetch("/api/ads/serve", {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
					},
					body: JSON.stringify({
						slotId,
						...userContext,
					}),
					signal: controller.signal,
				});

				clearTimeout(timeoutId);
				console.log(`[Ad Serving] Response received for slot ${slotId}:`, response.status, response.statusText);

				if (response.ok) {
					const result = await response.json();
					if (result.success && result.data) {
						setPaidAd(result.data);
						setRetryCount(0);

						// Log successful paid ad load
						logAdPerformance({
							slotId,
							paidAdSuccess: true,
							adsenseSuccess: false,
							loadTime: Date.now() - loadStartTime,
							fallbackUsed: false,
						});
						return;
					}
				}

				// If we get here, no paid ads were available (404) or other non-critical error
				if (response.status === 404) {
					// No ads available - this is expected, fall back to Google
					setRetryCount(0);
					return;
				}

				throw new Error(`HTTP ${response.status}: ${response.statusText}`);
			} catch (error) {
				console.error(
					`[Ad Serving] Error fetching paid ad for slot ${slotId} (attempt ${attempt + 1}):`,
					error
				);

				// Log the error
				logAdError({
					slotId,
					errorType:
						error instanceof Error && error.name === "AbortError"
							? "timeout"
							: error instanceof Error && error.message.includes("timeout")
							? "timeout"
							: "paid_ad_failure",
					errorMessage: error instanceof Error ? error.message : "Unknown error",
					retryCount: attempt + 1,
					sessionId,
				});

				if (attempt < maxRetries - 1) {
					// Exponential backoff: 1s, 2s, 4s
					const delay = Math.pow(2, attempt) * 1000;
					setTimeout(() => {
						setRetryCount(attempt + 1);
						fetchPaidAd(attempt + 1);
					}, delay);
				} else {
					setError("Failed to load paid ads after multiple attempts");
					setRetryCount(0);
					setFallbackMode("google");
				}
			} finally {
				if (attempt === 0 || attempt >= maxRetries - 1) {
					setIsLoading(false);
				}
			}
		};

		fetchPaidAd();
	}, [slotId, enablePaidAds, maxRetries]);

	// Track impression when ad becomes visible with error handling
	useEffect(() => {
		if (!paidAd || hasTrackedImpression) return;

		const observer = new IntersectionObserver(
			(entries) => {
				entries.forEach((entry) => {
					if (entry.isIntersecting && entry.intersectionRatio > 0.5) {
						trackAdImpression({
							ad_id: paidAd.id,
							session_id: sessionId,
							country_code: getCountryCode(),
							device_type: getDeviceType(navigator.userAgent),
						}).catch((error) => {
							console.error("Failed to track impression:", error);
							// Don't block the user experience for tracking failures
						});
						setHasTrackedImpression(true);
						observer.disconnect();
					}
				});
			},
			{ threshold: 0.5 }
		);

		if (adContainerRef.current) {
			observer.observe(adContainerRef.current);
		}

		return () => observer.disconnect();
	}, [paidAd, sessionId, hasTrackedImpression]);

	// Initialize Google AdSense when no paid ad is available
	useEffect(() => {
		if (isLoading || paidAd || googleAdInitialized.current) return;

		const container = adContainerRef.current;
		const adEl = container?.querySelector("ins.adsbygoogle");

		if (!adEl) return;

		// Clear any previous ad content
		adEl.innerHTML = "";

		const { width, height } = dimensions;

		// Apply sizing rules based on slot type
		const isHorizontal = width > height; // Leaderboard, Billboard, Banner types
		if (isHorizontal && container) {
			const parentWidth = container.parentElement?.clientWidth || width;
			const calculatedWidth = Math.min(parentWidth, width);
			const calculatedHeight = (height / width) * calculatedWidth;

			container.style.width = `${calculatedWidth}px`;
			container.style.height = `${calculatedHeight}px`;
		} else if (container) {
			container.style.width = `${width}px`;
			container.style.height = `${height}px`;
		}

		if (container) {
			container.style.overflow = "hidden";
			container.style.position = "relative";
		}

		// Enhanced Google AdSense loading with comprehensive error handling
		let googleRetryCount = 0;
		const loadGoogleAd = () => {
			try {
				// Clear any existing timeout
				if (googleAdLoadTimeout.current) {
					clearTimeout(googleAdLoadTimeout.current);
				}

				// Set timeout for Google AdSense loading
				googleAdLoadTimeout.current = setTimeout(() => {
					if (!googleAdInitialized.current) {
						console.error("Google AdSense load timeout for slot:", slotId);
						setGoogleAdError("AdSense load timeout");
						setFallbackMode("placeholder");

						// Log AdSense timeout
						logAdError({
							slotId,
							errorType: "timeout",
							errorMessage: "Google AdSense load timeout",
							sessionId,
							fallbackUsed: "placeholder",
						});
					}
				}, 10000); // 10 second timeout

				((window as any).adsbygoogle = (window as any).adsbygoogle || []).push({});
				googleAdInitialized.current = true;
				setFallbackMode("google");

				// Clear timeout on successful load
				if (googleAdLoadTimeout.current) {
					clearTimeout(googleAdLoadTimeout.current);
				}

				// Log successful AdSense load
				logAdPerformance({
					slotId,
					paidAdSuccess: false,
					adsenseSuccess: true,
					loadTime: Date.now() - loadStartTime,
					fallbackUsed: true,
				});
			} catch (e) {
				googleRetryCount++;
				console.error(`Google AdSense load error (attempt ${googleRetryCount}):`, e);

				if (googleRetryCount < 3) {
					setTimeout(loadGoogleAd, 1000 * googleRetryCount); // Progressive delay
				} else {
					console.error("Google AdSense load failed after 3 attempts for slot:", slotId);
					const errorMessage = `AdSense failed: ${e instanceof Error ? e.message : "Unknown error"}`;
					setGoogleAdError(errorMessage);
					setFallbackMode("placeholder");

					// Log AdSense failure
					logAdError({
						slotId,
						errorType: "adsense_failure",
						errorMessage,
						retryCount: googleRetryCount,
						sessionId,
						fallbackUsed: "placeholder",
					});
				}
			}
		};

		// Only load Google ads if we don't have a paid ad and no placeholder slot
		if (!googleAdSlot.includes("PLACEHOLDER")) {
			loadGoogleAd();
		} else {
			console.warn(`Placeholder Google AdSense slot detected for slotId ${slotId}: ${googleAdSlot}`);
			setFallbackMode("placeholder");
		}

		return () => {
			if (adEl) adEl.innerHTML = "";
		};
	}, [slotId, isLoading, paidAd]);

	// Handle paid ad click with error handling
	const handlePaidAdClick = async () => {
		if (!paidAd) return;

		try {
			// Track the click (don't wait for it to complete)
			trackAdClick({
				ad_id: paidAd.id,
				session_id: sessionId,
				country_code: getCountryCode(),
				device_type: getDeviceType(navigator.userAgent),
			}).catch((error) => {
				console.error("Failed to track click:", error);
				// Don't block the redirect for tracking failures
			});

			// Redirect to target URL immediately
			window.open(paidAd.target_url, "_blank", "noopener,noreferrer");
		} catch (error) {
			console.error("Error handling ad click:", error);
			// Still try to redirect even if tracking fails
			window.open(paidAd.target_url, "_blank", "noopener,noreferrer");
		}
	};

	// Validate that the required Google AdSense slot is provided
	if (!googleAdSlot || googleAdSlot.includes("PLACEHOLDER")) {
		console.error(`Invalid Google AdSense slot for slotId ${slotId}: ${googleAdSlot}`);
	}

	// Render fallback placeholder when both paid ads and AdSense fail
	const renderFallbackPlaceholder = () => {
		const { width, height } = dimensions;
		return (
			<div className={`inline-block ${className}`}>
				<div
					style={{
						width: `${width}px`,
						height: `${height}px`,
						margin: "0 auto",
						display: "flex",
						flexDirection: "column",
						alignItems: "center",
						justifyContent: "center",
						backgroundColor: "#f8fafc",
						border: "2px dashed #e2e8f0",
						borderRadius: "8px",
						color: "#64748b",
						fontSize: "14px",
						textAlign: "center",
						padding: "16px",
						boxSizing: "border-box",
					}}
				>
					<div style={{ marginBottom: "8px", fontSize: "16px" }}>📢</div>
					<div style={{ fontWeight: "500", marginBottom: "4px" }}>Advertisement Space</div>
					<div style={{ fontSize: "12px", opacity: 0.7 }}>
						{error && googleAdError
							? "Service temporarily unavailable"
							: googleAdSlot.includes("PLACEHOLDER")
							? "Slot configuration pending"
							: "Loading advertisement..."}
					</div>
					{process.env.NODE_ENV === "development" && (
						<div style={{ fontSize: "10px", marginTop: "8px", opacity: 0.5 }}>
							Slot {slotId} • {width}×{height}
						</div>
					)}
				</div>
			</div>
		);
	};

	// Show loading state
	if (isLoading && enablePaidAds) {
		const { width, height } = dimensions;
		return (
			<div className={`inline-block ${className}`}>
				<div
					style={{
						width: `${width}px`,
						height: `${height}px`,
						margin: "0 auto",
						display: "flex",
						alignItems: "center",
						justifyContent: "center",
						backgroundColor: "#f3f4f6",
						border: "1px solid #e5e7eb",
						borderRadius: "4px",
					}}
				>
					<div className="text-sm text-gray-500">
						{retryCount > 0 ? `Loading ads... (${retryCount}/${maxRetries})` : "Loading ads..."}
					</div>
				</div>
			</div>
		);
	}

	// Show fallback placeholder when both paid ads and AdSense fail
	if (fallbackMode === "placeholder") {
		return renderFallbackPlaceholder();
	}

	// Show error state (only if we have an error and no fallback)
	if (error && !enablePaidAds && fallbackMode === "none") {
		return renderFallbackPlaceholder();
	}

	// Render paid ad
	if (paidAd) {
		const { width, height } = dimensions;

		return (
			<div className={`inline-block ${className}`} ref={adContainerRef}>
				<div
					style={{
						width: `${width}px`,
						height: `${height}px`,
						margin: "0 auto",
						position: "relative",
						cursor: "pointer",
						overflow: "hidden",
						border: "1px solid #e5e7eb",
						borderRadius: "4px",
					}}
					onClick={handlePaidAdClick}
				>
					<img
						src={paidAd.image_url}
						alt={paidAd.title}
						style={{
							width: "100%",
							height: "100%",
							objectFit: "cover",
						}}
					/>
					<div
						style={{
							position: "absolute",
							top: "4px",
							right: "4px",
							background: "rgba(0,0,0,0.7)",
							color: "white",
							fontSize: "10px",
							padding: "2px 4px",
							borderRadius: "2px",
						}}
					>
						Sponsored
					</div>
				</div>
			</div>
		);
	}

	// Render Google AdSense fallback (only if not in placeholder mode)
	if (fallbackMode === "google" || (!error && !googleAdError)) {
		return (
			<div className={`inline-block ${className}`}>
				<div
					ref={adContainerRef}
					style={{
						minWidth: `${dimensions.width}px`,
						minHeight: `${dimensions.height}px`,
						margin: "0 auto",
					}}
				>
					<ins
						key={`${slotId}-${googleAdSlot}`}
						className="adsbygoogle"
						style={{ display: "block" }}
						data-ad-client="ca-pub-5681407322305640"
						data-ad-slot={googleAdSlot}
						data-ad-format={
							dimensions.width > dimensions.height
								? "horizontal"
								: dimensions.width === dimensions.height
								? "rectangle"
								: "auto"
						}
						data-full-width-responsive="true"
					/>
				</div>
			</div>
		);
	}

	// Final fallback - render placeholder
	return renderFallbackPlaceholder();
}
