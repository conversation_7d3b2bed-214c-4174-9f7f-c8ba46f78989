"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/subnets/[id]/page",{

/***/ "(app-pages-browser)/./components/ads-placements/smart-ad-banner.tsx":
/*!*******************************************************!*\
  !*** ./components/ads-placements/smart-ad-banner.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SmartAdBanner: () => (/* binding */ SmartAdBanner)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/api/ad-serving */ \"(app-pages-browser)/./lib/api/ad-serving.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ SmartAdBanner auto */ \nvar _s = $RefreshSig$();\n\n\n// Simple logging functions for ad performance tracking\nconst logAdPerformance = (data)=>{\n    if (true) {\n        console.log(\"Ad Performance:\", data);\n    }\n};\nconst logAdError = (data)=>{\n    if (true) {\n        console.error(\"Ad Error:\", data);\n    }\n};\nfunction SmartAdBanner(param) {\n    let { slotId, className, googleAdSlot, enablePaidAds = true } = param;\n    _s();\n    const [paidAd, setPaidAd] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [retryCount, setRetryCount] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [sessionId] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        \"SmartAdBanner.useState\": ()=>(0,_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.generateSessionId)()\n    }[\"SmartAdBanner.useState\"]);\n    const [hasTrackedImpression, setHasTrackedImpression] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [googleAdError, setGoogleAdError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [fallbackMode, setFallbackMode] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"none\");\n    const [loadStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        \"SmartAdBanner.useState\": ()=>Date.now()\n    }[\"SmartAdBanner.useState\"]);\n    const adContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const googleAdInitialized = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(false);\n    const googleAdLoadTimeout = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const maxRetries = 3;\n    // Get dimensions for this slot\n    const dimensions = _lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.SLOT_DIMENSIONS[slotId];\n    if (!dimensions) {\n        console.error(\"No dimensions found for slot ID: \".concat(slotId));\n        return null;\n    }\n    // Fetch paid ad on component mount with retry logic\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"SmartAdBanner.useEffect\": ()=>{\n            if (!enablePaidAds) {\n                setIsLoading(false);\n                return;\n            }\n            const fetchPaidAd = {\n                \"SmartAdBanner.useEffect.fetchPaidAd\": async function() {\n                    let attempt = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0;\n                    try {\n                        setError(null);\n                        const userContext = {\n                            country_code: (0,_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.getCountryCode)(),\n                            device_type: (0,_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.getDeviceType)(navigator.userAgent),\n                            language: (0,_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.getLanguage)(),\n                            user_agent: navigator.userAgent\n                        };\n                        console.log(\"[Ad Serving] Fetching paid ad for slot \".concat(slotId), userContext);\n                        const controller = new AbortController();\n                        const timeoutId = setTimeout({\n                            \"SmartAdBanner.useEffect.fetchPaidAd.timeoutId\": ()=>{\n                                console.log(\"[Ad Serving] Request timeout for slot \".concat(slotId));\n                                controller.abort();\n                            }\n                        }[\"SmartAdBanner.useEffect.fetchPaidAd.timeoutId\"], 10000); // Increased to 10 second timeout\n                        const response = await fetch(\"/api/ads/serve\", {\n                            method: \"POST\",\n                            headers: {\n                                \"Content-Type\": \"application/json\"\n                            },\n                            body: JSON.stringify({\n                                slotId,\n                                ...userContext\n                            }),\n                            signal: controller.signal\n                        });\n                        clearTimeout(timeoutId);\n                        console.log(\"[Ad Serving] Response received for slot \".concat(slotId, \":\"), response.status, response.statusText);\n                        if (response.ok) {\n                            const result = await response.json();\n                            if (result.success && result.data) {\n                                setPaidAd(result.data);\n                                setRetryCount(0);\n                                // Log successful paid ad load\n                                logAdPerformance({\n                                    slotId,\n                                    paidAdSuccess: true,\n                                    adsenseSuccess: false,\n                                    loadTime: Date.now() - loadStartTime,\n                                    fallbackUsed: false\n                                });\n                                return;\n                            }\n                        }\n                        // If we get here, no paid ads were available (404) or other non-critical error\n                        if (response.status === 404) {\n                            // No ads available - this is expected, fall back to Google\n                            setRetryCount(0);\n                            return;\n                        }\n                        throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n                    } catch (error) {\n                        console.error(\"[Ad Serving] Error fetching paid ad for slot \".concat(slotId, \" (attempt \").concat(attempt + 1, \"):\"), error);\n                        // Log the error\n                        logAdError({\n                            slotId,\n                            errorType: error instanceof Error && error.name === \"AbortError\" ? \"timeout\" : error instanceof Error && error.message.includes(\"timeout\") ? \"timeout\" : \"paid_ad_failure\",\n                            errorMessage: error instanceof Error ? error.message : \"Unknown error\",\n                            retryCount: attempt + 1,\n                            sessionId\n                        });\n                        if (attempt < maxRetries - 1) {\n                            // Exponential backoff: 1s, 2s, 4s\n                            const delay = Math.pow(2, attempt) * 1000;\n                            setTimeout({\n                                \"SmartAdBanner.useEffect.fetchPaidAd\": ()=>{\n                                    setRetryCount(attempt + 1);\n                                    fetchPaidAd(attempt + 1);\n                                }\n                            }[\"SmartAdBanner.useEffect.fetchPaidAd\"], delay);\n                        } else {\n                            setError(\"Failed to load paid ads after multiple attempts\");\n                            setRetryCount(0);\n                            setFallbackMode(\"google\");\n                        }\n                    } finally{\n                        if (attempt === 0 || attempt >= maxRetries - 1) {\n                            setIsLoading(false);\n                        }\n                    }\n                }\n            }[\"SmartAdBanner.useEffect.fetchPaidAd\"];\n            fetchPaidAd();\n        }\n    }[\"SmartAdBanner.useEffect\"], [\n        slotId,\n        enablePaidAds,\n        maxRetries\n    ]);\n    // Track impression when ad becomes visible with error handling\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"SmartAdBanner.useEffect\": ()=>{\n            if (!paidAd || hasTrackedImpression) return;\n            const observer = new IntersectionObserver({\n                \"SmartAdBanner.useEffect\": (entries)=>{\n                    entries.forEach({\n                        \"SmartAdBanner.useEffect\": (entry)=>{\n                            if (entry.isIntersecting && entry.intersectionRatio > 0.5) {\n                                (0,_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.trackAdImpression)({\n                                    ad_id: paidAd.id,\n                                    session_id: sessionId,\n                                    country_code: (0,_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.getCountryCode)(),\n                                    device_type: (0,_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.getDeviceType)(navigator.userAgent)\n                                }).catch({\n                                    \"SmartAdBanner.useEffect\": (error)=>{\n                                        console.error(\"Failed to track impression:\", error);\n                                    // Don't block the user experience for tracking failures\n                                    }\n                                }[\"SmartAdBanner.useEffect\"]);\n                                setHasTrackedImpression(true);\n                                observer.disconnect();\n                            }\n                        }\n                    }[\"SmartAdBanner.useEffect\"]);\n                }\n            }[\"SmartAdBanner.useEffect\"], {\n                threshold: 0.5\n            });\n            if (adContainerRef.current) {\n                observer.observe(adContainerRef.current);\n            }\n            return ({\n                \"SmartAdBanner.useEffect\": ()=>observer.disconnect()\n            })[\"SmartAdBanner.useEffect\"];\n        }\n    }[\"SmartAdBanner.useEffect\"], [\n        paidAd,\n        sessionId,\n        hasTrackedImpression\n    ]);\n    // Initialize Google AdSense when no paid ad is available\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"SmartAdBanner.useEffect\": ()=>{\n            if (isLoading || paidAd || googleAdInitialized.current) return;\n            const container = adContainerRef.current;\n            const adEl = container === null || container === void 0 ? void 0 : container.querySelector(\"ins.adsbygoogle\");\n            if (!adEl) return;\n            // Clear any previous ad content\n            adEl.innerHTML = \"\";\n            const { width, height } = dimensions;\n            // Apply sizing rules based on slot type\n            const isHorizontal = width > height; // Leaderboard, Billboard, Banner types\n            if (isHorizontal && container) {\n                var _container_parentElement;\n                const parentWidth = ((_container_parentElement = container.parentElement) === null || _container_parentElement === void 0 ? void 0 : _container_parentElement.clientWidth) || width;\n                const calculatedWidth = Math.min(parentWidth, width);\n                const calculatedHeight = height / width * calculatedWidth;\n                container.style.width = \"\".concat(calculatedWidth, \"px\");\n                container.style.height = \"\".concat(calculatedHeight, \"px\");\n            } else if (container) {\n                container.style.width = \"\".concat(width, \"px\");\n                container.style.height = \"\".concat(height, \"px\");\n            }\n            if (container) {\n                container.style.overflow = \"hidden\";\n                container.style.position = \"relative\";\n            }\n            // Enhanced Google AdSense loading with comprehensive error handling\n            let googleRetryCount = 0;\n            const loadGoogleAd = {\n                \"SmartAdBanner.useEffect.loadGoogleAd\": ()=>{\n                    try {\n                        // Clear any existing timeout\n                        if (googleAdLoadTimeout.current) {\n                            clearTimeout(googleAdLoadTimeout.current);\n                        }\n                        // Set timeout for Google AdSense loading\n                        googleAdLoadTimeout.current = setTimeout({\n                            \"SmartAdBanner.useEffect.loadGoogleAd\": ()=>{\n                                if (!googleAdInitialized.current) {\n                                    console.error(\"Google AdSense load timeout for slot:\", slotId);\n                                    setGoogleAdError(\"AdSense load timeout\");\n                                    setFallbackMode(\"placeholder\");\n                                    // Log AdSense timeout\n                                    logAdError({\n                                        slotId,\n                                        errorType: \"timeout\",\n                                        errorMessage: \"Google AdSense load timeout\",\n                                        sessionId,\n                                        fallbackUsed: \"placeholder\"\n                                    });\n                                }\n                            }\n                        }[\"SmartAdBanner.useEffect.loadGoogleAd\"], 10000); // 10 second timeout\n                        (window.adsbygoogle = window.adsbygoogle || []).push({});\n                        googleAdInitialized.current = true;\n                        setFallbackMode(\"google\");\n                        // Clear timeout on successful load\n                        if (googleAdLoadTimeout.current) {\n                            clearTimeout(googleAdLoadTimeout.current);\n                        }\n                        // Log successful AdSense load\n                        logAdPerformance({\n                            slotId,\n                            paidAdSuccess: false,\n                            adsenseSuccess: true,\n                            loadTime: Date.now() - loadStartTime,\n                            fallbackUsed: true\n                        });\n                    } catch (e) {\n                        googleRetryCount++;\n                        console.error(\"Google AdSense load error (attempt \".concat(googleRetryCount, \"):\"), e);\n                        if (googleRetryCount < 3) {\n                            setTimeout(loadGoogleAd, 1000 * googleRetryCount); // Progressive delay\n                        } else {\n                            console.error(\"Google AdSense load failed after 3 attempts for slot:\", slotId);\n                            const errorMessage = \"AdSense failed: \".concat(e instanceof Error ? e.message : \"Unknown error\");\n                            setGoogleAdError(errorMessage);\n                            setFallbackMode(\"placeholder\");\n                            // Log AdSense failure\n                            logAdError({\n                                slotId,\n                                errorType: \"adsense_failure\",\n                                errorMessage,\n                                retryCount: googleRetryCount,\n                                sessionId,\n                                fallbackUsed: \"placeholder\"\n                            });\n                        }\n                    }\n                }\n            }[\"SmartAdBanner.useEffect.loadGoogleAd\"];\n            // Only load Google ads if we don't have a paid ad and no placeholder slot\n            if (!googleAdSlot.includes(\"PLACEHOLDER\")) {\n                loadGoogleAd();\n            } else {\n                console.warn(\"Placeholder Google AdSense slot detected for slotId \".concat(slotId, \": \").concat(googleAdSlot));\n                setFallbackMode(\"placeholder\");\n            }\n            return ({\n                \"SmartAdBanner.useEffect\": ()=>{\n                    if (adEl) adEl.innerHTML = \"\";\n                }\n            })[\"SmartAdBanner.useEffect\"];\n        }\n    }[\"SmartAdBanner.useEffect\"], [\n        slotId,\n        isLoading,\n        paidAd\n    ]);\n    // Handle paid ad click with error handling\n    const handlePaidAdClick = async ()=>{\n        if (!paidAd) return;\n        try {\n            // Track the click (don't wait for it to complete)\n            (0,_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.trackAdClick)({\n                ad_id: paidAd.id,\n                session_id: sessionId,\n                country_code: (0,_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.getCountryCode)(),\n                device_type: (0,_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.getDeviceType)(navigator.userAgent)\n            }).catch((error)=>{\n                console.error(\"Failed to track click:\", error);\n            // Don't block the redirect for tracking failures\n            });\n            // Redirect to target URL immediately\n            window.open(paidAd.target_url, \"_blank\", \"noopener,noreferrer\");\n        } catch (error) {\n            console.error(\"Error handling ad click:\", error);\n            // Still try to redirect even if tracking fails\n            window.open(paidAd.target_url, \"_blank\", \"noopener,noreferrer\");\n        }\n    };\n    // Validate that the required Google AdSense slot is provided\n    if (!googleAdSlot || googleAdSlot.includes(\"PLACEHOLDER\")) {\n        console.error(\"Invalid Google AdSense slot for slotId \".concat(slotId, \": \").concat(googleAdSlot));\n    }\n    // Render fallback placeholder when both paid ads and AdSense fail\n    const renderFallbackPlaceholder = ()=>{\n        const { width, height } = dimensions;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"inline-block \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: \"\".concat(width, \"px\"),\n                    height: \"\".concat(height, \"px\"),\n                    margin: \"0 auto\",\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\",\n                    backgroundColor: \"#f8fafc\",\n                    border: \"2px dashed #e2e8f0\",\n                    borderRadius: \"8px\",\n                    color: \"#64748b\",\n                    fontSize: \"14px\",\n                    textAlign: \"center\",\n                    padding: \"16px\",\n                    boxSizing: \"border-box\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginBottom: \"8px\",\n                            fontSize: \"16px\"\n                        },\n                        children: \"\\uD83D\\uDCE2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                        lineNumber: 368,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontWeight: \"500\",\n                            marginBottom: \"4px\"\n                        },\n                        children: \"Advertisement Space\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                        lineNumber: 369,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"12px\",\n                            opacity: 0.7\n                        },\n                        children: error && googleAdError ? \"Service temporarily unavailable\" : googleAdSlot.includes(\"PLACEHOLDER\") ? \"Slot configuration pending\" : \"Loading advertisement...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 6\n                    }, this),\n                     true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"10px\",\n                            marginTop: \"8px\",\n                            opacity: 0.5\n                        },\n                        children: [\n                            \"Slot \",\n                            slotId,\n                            \" • \",\n                            width,\n                            \"\\xd7\",\n                            height\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                        lineNumber: 378,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                lineNumber: 349,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n            lineNumber: 348,\n            columnNumber: 4\n        }, this);\n    };\n    // Show loading state\n    if (isLoading && enablePaidAds) {\n        const { width, height } = dimensions;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"inline-block \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: \"\".concat(width, \"px\"),\n                    height: \"\".concat(height, \"px\"),\n                    margin: \"0 auto\",\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\",\n                    backgroundColor: \"#f3f4f6\",\n                    border: \"1px solid #e5e7eb\",\n                    borderRadius: \"4px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm text-gray-500\",\n                    children: retryCount > 0 ? \"Loading ads... (\".concat(retryCount, \"/\").concat(maxRetries, \")\") : \"Loading ads...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                    lineNumber: 405,\n                    columnNumber: 6\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                lineNumber: 392,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n            lineNumber: 391,\n            columnNumber: 4\n        }, this);\n    }\n    // Show fallback placeholder when both paid ads and AdSense fail\n    if (fallbackMode === \"placeholder\") {\n        return renderFallbackPlaceholder();\n    }\n    // Show error state (only if we have an error and no fallback)\n    if (error && !enablePaidAds && fallbackMode === \"none\") {\n        return renderFallbackPlaceholder();\n    }\n    // Render paid ad\n    if (paidAd) {\n        const { width, height } = dimensions;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"inline-block \".concat(className),\n            ref: adContainerRef,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: \"\".concat(width, \"px\"),\n                    height: \"\".concat(height, \"px\"),\n                    margin: \"0 auto\",\n                    position: \"relative\",\n                    cursor: \"pointer\",\n                    overflow: \"hidden\",\n                    border: \"1px solid #e5e7eb\",\n                    borderRadius: \"4px\"\n                },\n                onClick: handlePaidAdClick,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: paidAd.image_url,\n                        alt: paidAd.title,\n                        style: {\n                            width: \"100%\",\n                            height: \"100%\",\n                            objectFit: \"cover\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                        lineNumber: 442,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"absolute\",\n                            top: \"4px\",\n                            right: \"4px\",\n                            background: \"rgba(0,0,0,0.7)\",\n                            color: \"white\",\n                            fontSize: \"10px\",\n                            padding: \"2px 4px\",\n                            borderRadius: \"2px\"\n                        },\n                        children: \"Sponsored\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                        lineNumber: 451,\n                        columnNumber: 6\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                lineNumber: 429,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n            lineNumber: 428,\n            columnNumber: 4\n        }, this);\n    }\n    // Render Google AdSense fallback (only if not in placeholder mode)\n    if (fallbackMode === \"google\" || !error && !googleAdError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"inline-block \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: adContainerRef,\n                style: {\n                    minWidth: \"\".concat(dimensions.width, \"px\"),\n                    minHeight: \"\".concat(dimensions.height, \"px\"),\n                    margin: \"0 auto\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ins\", {\n                    className: \"adsbygoogle\",\n                    style: {\n                        display: \"block\"\n                    },\n                    \"data-ad-client\": \"ca-pub-5681407322305640\",\n                    \"data-ad-slot\": googleAdSlot,\n                    \"data-ad-format\": dimensions.width > dimensions.height ? \"horizontal\" : dimensions.width === dimensions.height ? \"rectangle\" : \"auto\",\n                    \"data-full-width-responsive\": \"true\"\n                }, \"\".concat(slotId, \"-\").concat(googleAdSlot), false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                    lineNumber: 482,\n                    columnNumber: 6\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                lineNumber: 474,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n            lineNumber: 473,\n            columnNumber: 4\n        }, this);\n    }\n    // Final fallback - render placeholder\n    return renderFallbackPlaceholder();\n}\n_s(SmartAdBanner, \"1JBpCQtIfTae8lbGZzGSXCQ6b20=\");\n_c = SmartAdBanner;\nvar _c;\n$RefreshReg$(_c, \"SmartAdBanner\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ads-placements/smart-ad-banner.tsx\n"));

/***/ })

});