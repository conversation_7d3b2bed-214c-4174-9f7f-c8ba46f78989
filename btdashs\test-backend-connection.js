// Simple test to check backend API connection
const API_BASE = "http://localhost:5000/api";
const INTERNAL_API_KEY = "WVcQAfUkPxMt3nGanMWWBLUIAaw8LJdoBffYbzzCCLs";

console.log("Testing if backend is running on port 5000...");

async function testBackendConnection() {
	console.log("Testing backend API connection...");
	console.log("API_BASE:", API_BASE);

	try {
		// Test basic connection
		const response = await fetch(`${API_BASE}/serve-ad?slot=1`, {
			headers: {
				"x-internal-api-key": INTERNAL_API_KEY,
			},
		});

		console.log("Response status:", response.status);
		console.log("Response headers:", Object.fromEntries(response.headers.entries()));

		if (response.ok) {
			const data = await response.json();
			console.log("Response data:", data);
		} else {
			const errorText = await response.text();
			console.log("Error response:", errorText);
		}
	} catch (error) {
		console.error("Connection error:", error.message);
		console.error("Error details:", error);
	}
}

testBackendConnection();
