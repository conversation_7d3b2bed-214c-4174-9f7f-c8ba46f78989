"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/subnets/[id]/page",{

/***/ "(app-pages-browser)/./components/subnets/subnet-profile.tsx":
/*!***********************************************!*\
  !*** ./components/subnets/subnet-profile.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SubnetProfile)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Book,Github,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book.js\");\n/* harmony import */ var _barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Book,Github,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Book,Github,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var swiper_modules__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! swiper/modules */ \"(app-pages-browser)/./node_modules/swiper/modules/index.mjs\");\n/* harmony import */ var swiper_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! swiper/react */ \"(app-pages-browser)/./node_modules/swiper/swiper-react.mjs\");\n/* harmony import */ var _components_category_tag__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/category-tag */ \"(app-pages-browser)/./components/category-tag.tsx\");\n/* harmony import */ var _components_subnet_documentation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/subnet-documentation */ \"(app-pages-browser)/./components/subnet-documentation.tsx\");\n/* harmony import */ var _components_subnet_github_contribution_graph__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/subnet-github-contribution-graph */ \"(app-pages-browser)/./components/subnet-github-contribution-graph.tsx\");\n/* harmony import */ var _components_subnet_news__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/subnet-news */ \"(app-pages-browser)/./components/subnet-news.tsx\");\n/* harmony import */ var _components_subnet_team__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/subnet-team */ \"(app-pages-browser)/./components/subnet-team.tsx\");\n/* harmony import */ var _components_subnet_validators__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/subnet-validators */ \"(app-pages-browser)/./components/subnet-validators.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _public_tao_logo_svg__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/public/tao-logo.svg */ \"(app-pages-browser)/./public/tao-logo.svg\");\n/* harmony import */ var _components_ads_placements_smart_ad_banner__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ads-placements/smart-ad-banner */ \"(app-pages-browser)/./components/ads-placements/smart-ad-banner.tsx\");\n/* harmony import */ var _components_subnets_subnet_relationship_chart__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/subnets/subnet-relationship-chart */ \"(app-pages-browser)/./components/subnets/subnet-relationship-chart.tsx\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var _subnet_applications__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../subnet-applications */ \"(app-pages-browser)/./components/subnet-applications.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n/* import { useRef, useState } from \"react\"; */ \n\n\n\n\n\n\n\n\n\n\n\n// Import the new chart component\n\n\n\n\nfunction SubnetProfile(param) {\n    let { subnet, metrics, categories, news, products, jobs, events, companies } = param;\n    var _subnet_subnet_ids, _subnet_images, _metrics_github_contributions;\n    /* const [imageError, setImageError] = useState(false);\r\n  const videoRef = useRef<HTMLVideoElement>(null);\r\n  const [isPlaying, setIsPlaying] = useState(false);\r\n  const [isMuted, setIsMuted] = useState(false); */ const netuid = subnet.netuid;\n    var _companies_length, _products_length, _events_length, _jobs_length, _categories_length, _metrics_validators_count, _news_length, _subnet_subnet_ids_length;\n    /* const images = subnet.images?.length\r\n    ? subnet.images\r\n    : [\r\n        \"https://via.placeholder.com/800x400?text=Image+1\",\r\n        \"https://via.placeholder.com/800x400?text=Image+2\",\r\n        \"https://via.placeholder.com/800x400?text=Image+3\",\r\n      ]; */ const data = {\n        companyCount: (_companies_length = companies === null || companies === void 0 ? void 0 : companies.length) !== null && _companies_length !== void 0 ? _companies_length : 0,\n        productCount: (_products_length = products === null || products === void 0 ? void 0 : products.length) !== null && _products_length !== void 0 ? _products_length : 0,\n        eventCount: (_events_length = events === null || events === void 0 ? void 0 : events.length) !== null && _events_length !== void 0 ? _events_length : 0,\n        jobCount: (_jobs_length = jobs === null || jobs === void 0 ? void 0 : jobs.length) !== null && _jobs_length !== void 0 ? _jobs_length : 0,\n        categoryCount: (_categories_length = categories === null || categories === void 0 ? void 0 : categories.length) !== null && _categories_length !== void 0 ? _categories_length : 0,\n        validatorCount: (_metrics_validators_count = metrics === null || metrics === void 0 ? void 0 : metrics.validators_count) !== null && _metrics_validators_count !== void 0 ? _metrics_validators_count : 0,\n        newsCount: (_news_length = news.length) !== null && _news_length !== void 0 ? _news_length : 0,\n        subnetCount: (_subnet_subnet_ids_length = subnet === null || subnet === void 0 ? void 0 : (_subnet_subnet_ids = subnet.subnet_ids) === null || _subnet_subnet_ids === void 0 ? void 0 : _subnet_subnet_ids.length) !== null && _subnet_subnet_ids_length !== void 0 ? _subnet_subnet_ids_length : 0\n    };\n    var _metrics_validators_count1, _metrics_emission;\n    const metricsCards = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardTitle, {\n                            children: \"Price\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-3xl font-bold\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    src: _public_tao_logo_svg__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                                    alt: \"TAO\",\n                                    width: 24,\n                                    height: 24,\n                                    className: \"inline-block pr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 7\n                                }, this),\n                                (metrics === null || metrics === void 0 ? void 0 : metrics.alpha_price_tao) != null && !isNaN(metrics.alpha_price_tao) ? Number(metrics.alpha_price_tao) < 0.01 ? Number(metrics.alpha_price_tao).toFixed(3) : Number(metrics.alpha_price_tao).toFixed(2) : \"0.00\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                lineNumber: 79,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardTitle, {\n                            children: \"Validators\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-3xl font-bold\",\n                            children: (_metrics_validators_count1 = metrics === null || metrics === void 0 ? void 0 : metrics.validators_count) !== null && _metrics_validators_count1 !== void 0 ? _metrics_validators_count1 : 0\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                lineNumber: 95,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardTitle, {\n                            children: \"Emission\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-3xl font-bold\",\n                            children: [\n                                (((_metrics_emission = metrics === null || metrics === void 0 ? void 0 : metrics.emission) !== null && _metrics_emission !== void 0 ? _metrics_emission : 0) / 1e7).toFixed(2),\n                                \"%\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                lineNumber: 104,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardTitle, {\n                            children: \"Miners\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-3xl font-bold \".concat(subnet.active_miners <= 5 ? \"text-red-500\" : subnet.active_miners <= 15 ? \"text-orange-500\" : \"text-green-500\"),\n                            children: subnet.active_miners || 0\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                lineNumber: 113,\n                columnNumber: 4\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n        lineNumber: 78,\n        columnNumber: 3\n    }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-8 px-6 sm:px-8 lg:px-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full mb-8\",\n                    style: {\n                        minHeight: \"90px\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ads_placements_smart_ad_banner__WEBPACK_IMPORTED_MODULE_14__.SmartAdBanner, {\n                        slotId: 7,\n                        googleAdSlot: \"3064454441\",\n                        className: \"mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 6\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 5\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8 grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 rounded-full bg-primary flex items-center justify-center text-black text-3xl font-bold\",\n                                            children: subnet.subnet_symbol || subnet.name.charAt(0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl font-bold\",\n                                                    children: subnet.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 9\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: [\n                                                        \"Subnet ID: \",\n                                                        netuid\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 9\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: [\n                                                        \"Coldkey:\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"https://taostats.io/account/\".concat(subnet.sub_address_pkey, \"/?utm_source=dynamictoamarketcap&utm_medium=referral&utm_campaign=subnet_profile\"),\n                                                            target: \"_blank\",\n                                                            rel: \"noopener noreferrer\",\n                                                            className: \"text-xs text-muted-foreground underline hover:text-primary\",\n                                                            children: subnet.sub_address_pkey\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 10\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 9\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-3 flex flex-wrap gap-2 text-muted-foreground\",\n                                                    children: categories && categories.length > 0 ? categories.map((category, id)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_category_tag__WEBPACK_IMPORTED_MODULE_4__.CategoryTag, {\n                                                            category: category.name\n                                                        }, id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 13\n                                                        }, this)) : null\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 9\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 8\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"my-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_17__.Markdown, {\n                                        children: subnet.description_short\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 8\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-4 mb-8\",\n                                    children: subnet.white_paper ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                        asChild: true,\n                                        size: \"sm\",\n                                        className: \"gap-2\",\n                                        variant: \"default\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: subnet.white_paper,\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 11\n                                                }, this),\n                                                \"Read White Paper\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 10\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 9\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                        size: \"sm\",\n                                        className: \"gap-2\",\n                                        disabled: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 10\n                                            }, this),\n                                            \"White Paper Unavailable\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 9\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 7\n                                }, this),\n                                subnet.images || subnet.main_video_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2 overflow-hidden\",\n                                    children: metricsCards\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 8\n                                }, this) : null\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 6\n                        }, this),\n                        subnet.main_video_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative pb-[56.25%] h-0 overflow-hidden rounded-lg shadow-lg border border-slate-200 dark:border-slate-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                                        className: \"absolute left-0 w-full h-full\",\n                                        src: subnet.main_video_url,\n                                        title: \"Subnet video\",\n                                        frameBorder: \"0\",\n                                        allow: \"autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\n                                        allowFullScreen: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 9\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 8\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-4 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: subnet.website_perm || \"https://subnet-website.example.com\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-12 h-12 rounded-full bg-blue-50 dark:bg-blue-900/30 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-6 w-6 text-blue-600 dark:text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-grow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-slate-900 dark:text-slate-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors\",\n                                                            children: \"Official Website\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 222,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-500 dark:text-slate-400 truncate\",\n                                                            children: subnet.website_perm ? \"\".concat(subnet.website_perm.replace(\"https://www.\", \"\").slice(0, 30)).concat(subnet.website_perm.replace(\"https://www.\", \"\").length > 40 ? \"...\" : \"\") : \"subnet-website.example.com\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 9\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: subnet.github_repo || \"https://github.com/example/subnet-repo\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-12 h-12 rounded-full bg-purple-50 dark:bg-purple-900/30 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-6 w-6 text-purple-600 dark:text-purple-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-grow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-slate-900 dark:text-slate-100 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors\",\n                                                            children: \"GitHub Repository\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 247,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-500 dark:text-slate-400 truncate\",\n                                                            children: subnet.github_repo ? \"\".concat(subnet.github_repo.replace(\"https://github.com/\", \"\").slice(0, 30)).concat(subnet.github_repo.replace(\"https://github.com/\", \"\").length > 40 ? \"...\" : \"\") : \"github.com/example/subnet-repo\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 250,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 9\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 8\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 7\n                        }, this) : !subnet.main_video_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                subnet.images ? null : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"gap-2 overflow-hidden\",\n                                    children: metricsCards\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 32\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"rounded-lg overflow-hidden shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_3__.Swiper, {\n                                        modules: [\n                                            swiper_modules__WEBPACK_IMPORTED_MODULE_2__.Navigation,\n                                            swiper_modules__WEBPACK_IMPORTED_MODULE_2__.Pagination\n                                        ],\n                                        navigation: true,\n                                        pagination: {\n                                            clickable: true\n                                        },\n                                        spaceBetween: 10,\n                                        slidesPerView: 1,\n                                        className: \"w-full h-full\",\n                                        children: (_subnet_images = subnet.images) === null || _subnet_images === void 0 ? void 0 : _subnet_images.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_3__.SwiperSlide, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: image,\n                                                    alt: \"Subnet Image \".concat(index + 1),\n                                                    width: 800,\n                                                    height: 400,\n                                                    className: \"w-full h-auto object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 12\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 11\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 9\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 8\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-4 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: subnet.website_perm || \"https://subnet-website.example.com\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-12 h-12 rounded-full bg-blue-50 dark:bg-blue-900/30 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-6 w-6 text-blue-600 dark:text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-grow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-slate-900 dark:text-slate-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors\",\n                                                            children: \"Official Website\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-500 dark:text-slate-400 truncate\",\n                                                            children: subnet.website_perm ? \"\".concat(subnet.website_perm.replace(\"https://www.\", \"\").slice(0, 30)).concat(subnet.website_perm.replace(\"https://www.\", \"\").length > 40 ? \"...\" : \"\") : \"subnet-website.example.com\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 305,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 9\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: subnet.github_repo || \"https://github.com/example/subnet-repo\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-12 h-12 rounded-full bg-purple-50 dark:bg-purple-900/30 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-6 w-6 text-purple-600 dark:text-purple-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-grow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-slate-900 dark:text-slate-100 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors\",\n                                                            children: \"GitHub Repository\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-500 dark:text-slate-400 truncate\",\n                                                            children: subnet.github_repo ? \"\".concat(subnet.github_repo.replace(\"https://github.com/\", \"\").slice(0, 30)).concat(subnet.github_repo.replace(\"https://github.com/\", \"\").length > 40 ? \"...\" : \"\") : \"github.com/example/subnet-repo\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 330,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 9\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 8\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 7\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \" gap-4 overflow-hidden max-[60px]\",\n                                    children: metricsCards\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 8\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-4 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: subnet.website_perm || \"https://subnet-website.example.com\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-12 h-12 rounded-full bg-blue-50 dark:bg-blue-900/30 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-6 w-6 text-blue-600 dark:text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-grow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-slate-900 dark:text-slate-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors\",\n                                                            children: \"Official Website\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-500 dark:text-slate-400 truncate\",\n                                                            children: subnet.website_perm ? \"\".concat(subnet.website_perm.replace(\"https://www.\", \"\").slice(0, 30)).concat(subnet.website_perm.replace(\"https://www.\", \"\").length > 40 ? \"...\" : \"\") : \"subnet-website.example.com\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 9\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: subnet.github_repo || \"https://github.com/example/subnet-repo\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-12 h-12 rounded-full bg-purple-50 dark:bg-purple-900/30 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-6 w-6 text-purple-600 dark:text-purple-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-grow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-slate-900 dark:text-slate-100 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors\",\n                                                            children: \"GitHub Repository\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 385,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-500 dark:text-slate-400 truncate\",\n                                                            children: subnet.github_repo ? \"\".concat(subnet.github_repo.replace(\"https://github.com/\", \"\").slice(0, 30)).concat(subnet.github_repo.replace(\"https://github.com/\", \"\").length > 40 ? \"...\" : \"\") : \"github.com/example/subnet-repo\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 9\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 8\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 7\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ads_placements_smart_ad_banner__WEBPACK_IMPORTED_MODULE_14__.SmartAdBanner, {\n                                slotId: 9,\n                                googleAdSlot: \"9765594162\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 7\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 407,\n                            columnNumber: 6\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 5\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-7 gap-4 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-span-5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_subnet_github_contribution_graph__WEBPACK_IMPORTED_MODULE_6__.SubnetGithubContributionGraph, {\n                                className: \"h-[360px]\",\n                                contributions: (metrics === null || metrics === void 0 ? void 0 : (_metrics_github_contributions = metrics.github_contributions) === null || _metrics_github_contributions === void 0 ? void 0 : _metrics_github_contributions.data) || []\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                lineNumber: 416,\n                                columnNumber: 7\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 415,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-[360px] col-span-2 overflow-visible\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_subnets_subnet_relationship_chart__WEBPACK_IMPORTED_MODULE_15__.SubnetRelationshipChart, {\n                                subnetId: subnet.name,\n                                data: data,\n                                className: \"h-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 7\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 423,\n                            columnNumber: 6\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                    lineNumber: 413,\n                    columnNumber: 5\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-0 mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_subnet_applications__WEBPACK_IMPORTED_MODULE_16__.SubnetApplications, {\n                        products: products\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 430,\n                        columnNumber: 6\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                    lineNumber: 429,\n                    columnNumber: 5\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full mb-8 flex justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ads_placements_smart_ad_banner__WEBPACK_IMPORTED_MODULE_14__.SmartAdBanner, {\n                        slotId: 10,\n                        googleAdSlot: \"6623305948\",\n                        className: \"mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 435,\n                        columnNumber: 6\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                    lineNumber: 434,\n                    columnNumber: 5\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.Tabs, {\n                    defaultValue: \"overview\",\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsList, {\n                            className: \"flex flex-wrap\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsTrigger, {\n                                    value: \"overview\",\n                                    children: \"Overview\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsTrigger, {\n                                    value: \"team\",\n                                    children: \"Team\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 442,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsTrigger, {\n                                    value: \"documentation\",\n                                    children: \"Documentation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsTrigger, {\n                                    value: \"validators\",\n                                    children: \"Validators\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 444,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsTrigger, {\n                                    value: \"news\",\n                                    children: \"News\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 445,\n                                    columnNumber: 7\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 440,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-1 gap-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsContent, {\n                                        value: \"overview\",\n                                        className: \"space-y-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardTitle, {\n                                                        className: \"text-lg\",\n                                                        children: \"Key Features\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid md:grid-cols-2 gap-4\",\n                                                        children: subnet.key_features && subnet.key_features.length > 0 ? subnet.key_features[0].map((feature, id)=>feature && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2 pb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium\",\n                                                                        children: feature.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                                        lineNumber: 462,\n                                                                        columnNumber: 17\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-muted-foreground\",\n                                                                        children: feature.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                                        lineNumber: 463,\n                                                                        columnNumber: 17\n                                                                    }, this)\n                                                                ]\n                                                            }, id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 16\n                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-muted-foreground\",\n                                                            children: \"No key features available.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 470,\n                                                            columnNumber: 13\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                        lineNumber: 456,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 9\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 8\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsContent, {\n                                        value: \"team\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_subnet_team__WEBPACK_IMPORTED_MODULE_8__.SubnetTeam, {\n                                            subnet: subnet\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 9\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 477,\n                                        columnNumber: 8\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsContent, {\n                                        value: \"documentation\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_subnet_documentation__WEBPACK_IMPORTED_MODULE_5__.SubnetDocumentation, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 9\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 480,\n                                        columnNumber: 8\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsContent, {\n                                        value: \"validators\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_subnet_validators__WEBPACK_IMPORTED_MODULE_9__.SubnetValidators, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 9\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 8\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsContent, {\n                                        value: \"news\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_subnet_news__WEBPACK_IMPORTED_MODULE_7__.SubnetNews, {\n                                            news: news\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 487,\n                                            columnNumber: 9\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 8\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                lineNumber: 449,\n                                columnNumber: 7\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 448,\n                            columnNumber: 6\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                    lineNumber: 439,\n                    columnNumber: 5\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 mt-8 mb-8 min-h-[90px] w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ads_placements_smart_ad_banner__WEBPACK_IMPORTED_MODULE_14__.SmartAdBanner, {\n                        slotId: 8,\n                        googleAdSlot: \"SUBNET_MED_RECT_PLACEHOLDER\",\n                        className: \"w-full h-full\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 495,\n                        columnNumber: 6\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                    lineNumber: 494,\n                    columnNumber: 5\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n            lineNumber: 136,\n            columnNumber: 4\n        }, this)\n    }, void 0, false);\n}\n_c = SubnetProfile;\nvar _c;\n$RefreshReg$(_c, \"SubnetProfile\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/subnets/subnet-profile.tsx\n"));

/***/ })

});