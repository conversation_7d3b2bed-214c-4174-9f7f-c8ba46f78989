"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/subnets/[id]/page",{

/***/ "(app-pages-browser)/./components/ads-placements/smart-ad-banner.tsx":
/*!*******************************************************!*\
  !*** ./components/ads-placements/smart-ad-banner.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SmartAdBanner: () => (/* binding */ SmartAdBanner)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/api/ad-serving */ \"(app-pages-browser)/./lib/api/ad-serving.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ SmartAdBanner auto */ \nvar _s = $RefreshSig$();\n\n\n// Simple logging functions for ad performance tracking\nconst logAdPerformance = (data)=>{\n    if (true) {\n        console.log(\"Ad Performance:\", data);\n    }\n};\nconst logAdError = (data)=>{\n    if (true) {\n        console.error(\"Ad Error:\", data);\n    }\n};\nfunction SmartAdBanner(param) {\n    let { slotId, className, googleAdSlot, enablePaidAds = true } = param;\n    _s();\n    const [paidAd, setPaidAd] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [retryCount, setRetryCount] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [sessionId] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        \"SmartAdBanner.useState\": ()=>(0,_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.generateSessionId)()\n    }[\"SmartAdBanner.useState\"]);\n    const [hasTrackedImpression, setHasTrackedImpression] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [googleAdError, setGoogleAdError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [fallbackMode, setFallbackMode] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"none\");\n    const [loadStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        \"SmartAdBanner.useState\": ()=>Date.now()\n    }[\"SmartAdBanner.useState\"]);\n    const adContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const googleAdInitialized = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(false);\n    const googleAdLoadTimeout = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const maxRetries = 3;\n    // Get dimensions for this slot\n    const dimensions = _lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.SLOT_DIMENSIONS[slotId];\n    if (!dimensions) {\n        console.error(\"No dimensions found for slot ID: \".concat(slotId));\n        return null;\n    }\n    // Fetch paid ad on component mount with retry logic\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"SmartAdBanner.useEffect\": ()=>{\n            if (!enablePaidAds) {\n                setIsLoading(false);\n                return;\n            }\n            const fetchPaidAd = {\n                \"SmartAdBanner.useEffect.fetchPaidAd\": async function() {\n                    let attempt = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0;\n                    try {\n                        setError(null);\n                        const userContext = {\n                            country_code: (0,_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.getCountryCode)(),\n                            device_type: (0,_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.getDeviceType)(navigator.userAgent),\n                            language: (0,_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.getLanguage)(),\n                            user_agent: navigator.userAgent\n                        };\n                        console.log(\"[Ad Serving] Fetching paid ad for slot \".concat(slotId), userContext);\n                        const controller = new AbortController();\n                        const timeoutId = setTimeout({\n                            \"SmartAdBanner.useEffect.fetchPaidAd.timeoutId\": ()=>{\n                                console.log(\"[Ad Serving] Request timeout for slot \".concat(slotId));\n                                controller.abort();\n                            }\n                        }[\"SmartAdBanner.useEffect.fetchPaidAd.timeoutId\"], 10000); // Increased to 10 second timeout\n                        // Call backend API directly\n                        const API_BASE = process.env.NEXT_PUBLIC_API_BASE_URL || \"http://localhost:5000/api\";\n                        const params = new URLSearchParams({\n                            slot: slotId.toString(),\n                            ...userContext.country_code && {\n                                country_code: userContext.country_code\n                            },\n                            ...userContext.device_type && {\n                                device_type: userContext.device_type\n                            },\n                            ...userContext.language && {\n                                language: userContext.language\n                            },\n                            ...userContext.user_agent && {\n                                user_agent: userContext.user_agent\n                            }\n                        });\n                        const response = await fetch(\"\".concat(API_BASE, \"/serve-ad?\").concat(params), {\n                            method: \"GET\",\n                            headers: {\n                                \"x-internal-api-key\": process.env.NEXT_PUBLIC_INTERNAL_API_KEY || \"\"\n                            },\n                            signal: controller.signal\n                        });\n                        clearTimeout(timeoutId);\n                        console.log(\"[Ad Serving] Response received for slot \".concat(slotId, \":\"), response.status, response.statusText);\n                        if (response.ok) {\n                            const result = await response.json();\n                            if (result.success && result.data) {\n                                setPaidAd(result.data);\n                                setRetryCount(0);\n                                // Log successful paid ad load\n                                logAdPerformance({\n                                    slotId,\n                                    paidAdSuccess: true,\n                                    adsenseSuccess: false,\n                                    loadTime: Date.now() - loadStartTime,\n                                    fallbackUsed: false\n                                });\n                                return;\n                            }\n                        }\n                        // If we get here, no paid ads were available (404) or other non-critical error\n                        if (response.status === 404) {\n                            // No ads available - this is expected, fall back to Google\n                            setRetryCount(0);\n                            return;\n                        }\n                        throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n                    } catch (error) {\n                        console.error(\"[Ad Serving] Error fetching paid ad for slot \".concat(slotId, \" (attempt \").concat(attempt + 1, \"):\"), error);\n                        // Log the error\n                        logAdError({\n                            slotId,\n                            errorType: error instanceof Error && error.name === \"AbortError\" ? \"timeout\" : error instanceof Error && error.message.includes(\"timeout\") ? \"timeout\" : \"paid_ad_failure\",\n                            errorMessage: error instanceof Error ? error.message : \"Unknown error\",\n                            retryCount: attempt + 1,\n                            sessionId\n                        });\n                        if (attempt < maxRetries - 1) {\n                            // Exponential backoff: 1s, 2s, 4s\n                            const delay = Math.pow(2, attempt) * 1000;\n                            setTimeout({\n                                \"SmartAdBanner.useEffect.fetchPaidAd\": ()=>{\n                                    setRetryCount(attempt + 1);\n                                    fetchPaidAd(attempt + 1);\n                                }\n                            }[\"SmartAdBanner.useEffect.fetchPaidAd\"], delay);\n                        } else {\n                            setError(\"Failed to load paid ads after multiple attempts\");\n                            setRetryCount(0);\n                            setFallbackMode(\"google\");\n                        }\n                    } finally{\n                        if (attempt === 0 || attempt >= maxRetries - 1) {\n                            setIsLoading(false);\n                        }\n                    }\n                }\n            }[\"SmartAdBanner.useEffect.fetchPaidAd\"];\n            fetchPaidAd();\n        }\n    }[\"SmartAdBanner.useEffect\"], [\n        slotId,\n        enablePaidAds,\n        maxRetries\n    ]);\n    // Track impression when ad becomes visible with error handling\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"SmartAdBanner.useEffect\": ()=>{\n            if (!paidAd || hasTrackedImpression) return;\n            const observer = new IntersectionObserver({\n                \"SmartAdBanner.useEffect\": (entries)=>{\n                    entries.forEach({\n                        \"SmartAdBanner.useEffect\": (entry)=>{\n                            if (entry.isIntersecting && entry.intersectionRatio > 0.5) {\n                                (0,_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.trackAdImpression)({\n                                    ad_id: paidAd.id,\n                                    session_id: sessionId,\n                                    country_code: (0,_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.getCountryCode)(),\n                                    device_type: (0,_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.getDeviceType)(navigator.userAgent)\n                                }).catch({\n                                    \"SmartAdBanner.useEffect\": (error)=>{\n                                        console.error(\"Failed to track impression:\", error);\n                                    // Don't block the user experience for tracking failures\n                                    }\n                                }[\"SmartAdBanner.useEffect\"]);\n                                setHasTrackedImpression(true);\n                                observer.disconnect();\n                            }\n                        }\n                    }[\"SmartAdBanner.useEffect\"]);\n                }\n            }[\"SmartAdBanner.useEffect\"], {\n                threshold: 0.5\n            });\n            if (adContainerRef.current) {\n                observer.observe(adContainerRef.current);\n            }\n            return ({\n                \"SmartAdBanner.useEffect\": ()=>observer.disconnect()\n            })[\"SmartAdBanner.useEffect\"];\n        }\n    }[\"SmartAdBanner.useEffect\"], [\n        paidAd,\n        sessionId,\n        hasTrackedImpression\n    ]);\n    // Initialize Google AdSense when no paid ad is available\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"SmartAdBanner.useEffect\": ()=>{\n            if (isLoading || paidAd || googleAdInitialized.current) return;\n            const container = adContainerRef.current;\n            const adEl = container === null || container === void 0 ? void 0 : container.querySelector(\"ins.adsbygoogle\");\n            if (!adEl) return;\n            // Clear any previous ad content\n            adEl.innerHTML = \"\";\n            const { width, height } = dimensions;\n            // Apply sizing rules based on slot type\n            const isHorizontal = width > height; // Leaderboard, Billboard, Banner types\n            if (isHorizontal && container) {\n                var _container_parentElement;\n                const parentWidth = ((_container_parentElement = container.parentElement) === null || _container_parentElement === void 0 ? void 0 : _container_parentElement.clientWidth) || width;\n                const calculatedWidth = Math.min(parentWidth, width);\n                const calculatedHeight = height / width * calculatedWidth;\n                container.style.width = \"\".concat(calculatedWidth, \"px\");\n                container.style.height = \"\".concat(calculatedHeight, \"px\");\n            } else if (container) {\n                container.style.width = \"\".concat(width, \"px\");\n                container.style.height = \"\".concat(height, \"px\");\n            }\n            if (container) {\n                container.style.overflow = \"hidden\";\n                container.style.position = \"relative\";\n            }\n            // Enhanced Google AdSense loading with comprehensive error handling\n            let googleRetryCount = 0;\n            const loadGoogleAd = {\n                \"SmartAdBanner.useEffect.loadGoogleAd\": ()=>{\n                    try {\n                        // Clear any existing timeout\n                        if (googleAdLoadTimeout.current) {\n                            clearTimeout(googleAdLoadTimeout.current);\n                        }\n                        // Set timeout for Google AdSense loading\n                        googleAdLoadTimeout.current = setTimeout({\n                            \"SmartAdBanner.useEffect.loadGoogleAd\": ()=>{\n                                if (!googleAdInitialized.current) {\n                                    console.error(\"Google AdSense load timeout for slot:\", slotId);\n                                    setGoogleAdError(\"AdSense load timeout\");\n                                    setFallbackMode(\"placeholder\");\n                                    // Log AdSense timeout\n                                    logAdError({\n                                        slotId,\n                                        errorType: \"timeout\",\n                                        errorMessage: \"Google AdSense load timeout\",\n                                        sessionId,\n                                        fallbackUsed: \"placeholder\"\n                                    });\n                                }\n                            }\n                        }[\"SmartAdBanner.useEffect.loadGoogleAd\"], 10000); // 10 second timeout\n                        (window.adsbygoogle = window.adsbygoogle || []).push({});\n                        googleAdInitialized.current = true;\n                        setFallbackMode(\"google\");\n                        // Clear timeout on successful load\n                        if (googleAdLoadTimeout.current) {\n                            clearTimeout(googleAdLoadTimeout.current);\n                        }\n                        // Log successful AdSense load\n                        logAdPerformance({\n                            slotId,\n                            paidAdSuccess: false,\n                            adsenseSuccess: true,\n                            loadTime: Date.now() - loadStartTime,\n                            fallbackUsed: true\n                        });\n                    } catch (e) {\n                        googleRetryCount++;\n                        console.error(\"Google AdSense load error (attempt \".concat(googleRetryCount, \"):\"), e);\n                        if (googleRetryCount < 3) {\n                            setTimeout(loadGoogleAd, 1000 * googleRetryCount); // Progressive delay\n                        } else {\n                            console.error(\"Google AdSense load failed after 3 attempts for slot:\", slotId);\n                            const errorMessage = \"AdSense failed: \".concat(e instanceof Error ? e.message : \"Unknown error\");\n                            setGoogleAdError(errorMessage);\n                            setFallbackMode(\"placeholder\");\n                            // Log AdSense failure\n                            logAdError({\n                                slotId,\n                                errorType: \"adsense_failure\",\n                                errorMessage,\n                                retryCount: googleRetryCount,\n                                sessionId,\n                                fallbackUsed: \"placeholder\"\n                            });\n                        }\n                    }\n                }\n            }[\"SmartAdBanner.useEffect.loadGoogleAd\"];\n            // Only load Google ads if we don't have a paid ad and no placeholder slot\n            if (!googleAdSlot.includes(\"PLACEHOLDER\")) {\n                loadGoogleAd();\n            } else {\n                console.warn(\"Placeholder Google AdSense slot detected for slotId \".concat(slotId, \": \").concat(googleAdSlot));\n                setFallbackMode(\"placeholder\");\n            }\n            return ({\n                \"SmartAdBanner.useEffect\": ()=>{\n                    if (adEl) adEl.innerHTML = \"\";\n                }\n            })[\"SmartAdBanner.useEffect\"];\n        }\n    }[\"SmartAdBanner.useEffect\"], [\n        slotId,\n        isLoading,\n        paidAd\n    ]);\n    // Handle paid ad click with error handling\n    const handlePaidAdClick = async ()=>{\n        if (!paidAd) return;\n        try {\n            // Track the click (don't wait for it to complete)\n            (0,_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.trackAdClick)({\n                ad_id: paidAd.id,\n                session_id: sessionId,\n                country_code: (0,_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.getCountryCode)(),\n                device_type: (0,_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.getDeviceType)(navigator.userAgent)\n            }).catch((error)=>{\n                console.error(\"Failed to track click:\", error);\n            // Don't block the redirect for tracking failures\n            });\n            // Redirect to target URL immediately\n            window.open(paidAd.target_url, \"_blank\", \"noopener,noreferrer\");\n        } catch (error) {\n            console.error(\"Error handling ad click:\", error);\n            // Still try to redirect even if tracking fails\n            window.open(paidAd.target_url, \"_blank\", \"noopener,noreferrer\");\n        }\n    };\n    // Validate that the required Google AdSense slot is provided\n    if (!googleAdSlot || googleAdSlot.includes(\"PLACEHOLDER\")) {\n        console.error(\"Invalid Google AdSense slot for slotId \".concat(slotId, \": \").concat(googleAdSlot));\n    }\n    // Render fallback placeholder when both paid ads and AdSense fail\n    const renderFallbackPlaceholder = ()=>{\n        const { width, height } = dimensions;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"inline-block \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: \"\".concat(width, \"px\"),\n                    height: \"\".concat(height, \"px\"),\n                    margin: \"0 auto\",\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\",\n                    backgroundColor: \"#f8fafc\",\n                    border: \"2px dashed #e2e8f0\",\n                    borderRadius: \"8px\",\n                    color: \"#64748b\",\n                    fontSize: \"14px\",\n                    textAlign: \"center\",\n                    padding: \"16px\",\n                    boxSizing: \"border-box\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginBottom: \"8px\",\n                            fontSize: \"16px\"\n                        },\n                        children: \"\\uD83D\\uDCE2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                        lineNumber: 374,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontWeight: \"500\",\n                            marginBottom: \"4px\"\n                        },\n                        children: \"Advertisement Space\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                        lineNumber: 375,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"12px\",\n                            opacity: 0.7\n                        },\n                        children: error && googleAdError ? \"Service temporarily unavailable\" : googleAdSlot.includes(\"PLACEHOLDER\") ? \"Slot configuration pending\" : \"Loading advertisement...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                        lineNumber: 376,\n                        columnNumber: 6\n                    }, this),\n                     true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"10px\",\n                            marginTop: \"8px\",\n                            opacity: 0.5\n                        },\n                        children: [\n                            \"Slot \",\n                            slotId,\n                            \" • \",\n                            width,\n                            \"\\xd7\",\n                            height\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                        lineNumber: 384,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                lineNumber: 355,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n            lineNumber: 354,\n            columnNumber: 4\n        }, this);\n    };\n    // Show loading state\n    if (isLoading && enablePaidAds) {\n        const { width, height } = dimensions;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"inline-block \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: \"\".concat(width, \"px\"),\n                    height: \"\".concat(height, \"px\"),\n                    margin: \"0 auto\",\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\",\n                    backgroundColor: \"#f3f4f6\",\n                    border: \"1px solid #e5e7eb\",\n                    borderRadius: \"4px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm text-gray-500\",\n                    children: retryCount > 0 ? \"Loading ads... (\".concat(retryCount, \"/\").concat(maxRetries, \")\") : \"Loading ads...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                    lineNumber: 411,\n                    columnNumber: 6\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                lineNumber: 398,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n            lineNumber: 397,\n            columnNumber: 4\n        }, this);\n    }\n    // Show fallback placeholder when both paid ads and AdSense fail\n    if (fallbackMode === \"placeholder\") {\n        return renderFallbackPlaceholder();\n    }\n    // Show error state (only if we have an error and no fallback)\n    if (error && !enablePaidAds && fallbackMode === \"none\") {\n        return renderFallbackPlaceholder();\n    }\n    // Render paid ad\n    if (paidAd) {\n        const { width, height } = dimensions;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"inline-block \".concat(className),\n            ref: adContainerRef,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: \"\".concat(width, \"px\"),\n                    height: \"\".concat(height, \"px\"),\n                    margin: \"0 auto\",\n                    position: \"relative\",\n                    cursor: \"pointer\",\n                    overflow: \"hidden\",\n                    border: \"1px solid #e5e7eb\",\n                    borderRadius: \"4px\"\n                },\n                onClick: handlePaidAdClick,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: paidAd.image_url,\n                        alt: paidAd.title,\n                        style: {\n                            width: \"100%\",\n                            height: \"100%\",\n                            objectFit: \"cover\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                        lineNumber: 448,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"absolute\",\n                            top: \"4px\",\n                            right: \"4px\",\n                            background: \"rgba(0,0,0,0.7)\",\n                            color: \"white\",\n                            fontSize: \"10px\",\n                            padding: \"2px 4px\",\n                            borderRadius: \"2px\"\n                        },\n                        children: \"Sponsored\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                        lineNumber: 457,\n                        columnNumber: 6\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                lineNumber: 435,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n            lineNumber: 434,\n            columnNumber: 4\n        }, this);\n    }\n    // Render Google AdSense fallback (only if not in placeholder mode)\n    if (fallbackMode === \"google\" || !error && !googleAdError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"inline-block \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: adContainerRef,\n                style: {\n                    minWidth: \"\".concat(dimensions.width, \"px\"),\n                    minHeight: \"\".concat(dimensions.height, \"px\"),\n                    margin: \"0 auto\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ins\", {\n                    className: \"adsbygoogle\",\n                    style: {\n                        display: \"block\"\n                    },\n                    \"data-ad-client\": \"ca-pub-5681407322305640\",\n                    \"data-ad-slot\": googleAdSlot,\n                    \"data-ad-format\": dimensions.width > dimensions.height ? \"horizontal\" : dimensions.width === dimensions.height ? \"rectangle\" : \"auto\",\n                    \"data-full-width-responsive\": \"true\"\n                }, \"\".concat(slotId, \"-\").concat(googleAdSlot), false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                    lineNumber: 488,\n                    columnNumber: 6\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                lineNumber: 480,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n            lineNumber: 479,\n            columnNumber: 4\n        }, this);\n    }\n    // Final fallback - render placeholder\n    return renderFallbackPlaceholder();\n}\n_s(SmartAdBanner, \"1JBpCQtIfTae8lbGZzGSXCQ6b20=\");\n_c = SmartAdBanner;\nvar _c;\n$RefreshReg$(_c, \"SmartAdBanner\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ads-placements/smart-ad-banner.tsx\n"));

/***/ })

});