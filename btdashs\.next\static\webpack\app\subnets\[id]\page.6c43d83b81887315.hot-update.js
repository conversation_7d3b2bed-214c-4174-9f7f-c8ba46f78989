"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/subnets/[id]/page",{

/***/ "(app-pages-browser)/./lib/api/ad-serving.ts":
/*!*******************************!*\
  !*** ./lib/api/ad-serving.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AD_SLOTS: () => (/* binding */ AD_SLOTS),\n/* harmony export */   GOOGLE_AD_SLOTS: () => (/* binding */ GOOGLE_AD_SLOTS),\n/* harmony export */   SLOT_DIMENSIONS: () => (/* binding */ SLOT_DIMENSIONS),\n/* harmony export */   generateSessionId: () => (/* binding */ generateSessionId),\n/* harmony export */   getCountryCode: () => (/* binding */ getCountryCode),\n/* harmony export */   getDeviceType: () => (/* binding */ getDeviceType),\n/* harmony export */   getLanguage: () => (/* binding */ getLanguage),\n/* harmony export */   trackAdClick: () => (/* binding */ trackAdClick),\n/* harmony export */   trackAdImpression: () => (/* binding */ trackAdImpression)\n/* harmony export */ });\n// Database ad slot IDs and their configurations\nconst AD_SLOTS = {\n    // Home Page Slots (1-4)\n    HOME_LEADERBOARD: 1,\n    HOME_BILLBOARD: 2,\n    HOME_MEDIUM_RECTANGLE: 3,\n    HOME_SKYSCRAPER: 4,\n    // Subnet Page Slots (7-10)\n    SUBNET_MEDIUM_RECTANGLE: 7,\n    SUBNET_HALF_PAGE: 8,\n    SUBNET_BANNER: 9,\n    SUBNET_WIDE_SKYSCRAPER: 10,\n    // Company Page Slots (11-14)\n    COMPANY_MEDIUM_RECTANGLE: 11,\n    COMPANY_HALF_PAGE: 12,\n    COMPANY_LEADERBOARD: 13,\n    COMPANY_SQUARE_BUTTON: 14,\n    // Global Slots (15-16)\n    GLOBAL_POPUP: 15,\n    GLOBAL_STICKY_FOOTER: 16\n};\n// Slot dimensions mapping\nconst SLOT_DIMENSIONS = {\n    [AD_SLOTS.HOME_LEADERBOARD]: {\n        width: 728,\n        height: 90\n    },\n    [AD_SLOTS.HOME_BILLBOARD]: {\n        width: 970,\n        height: 250\n    },\n    [AD_SLOTS.HOME_MEDIUM_RECTANGLE]: {\n        width: 300,\n        height: 250\n    },\n    [AD_SLOTS.HOME_SKYSCRAPER]: {\n        width: 160,\n        height: 600\n    },\n    [AD_SLOTS.SUBNET_MEDIUM_RECTANGLE]: {\n        width: 300,\n        height: 250\n    },\n    [AD_SLOTS.SUBNET_HALF_PAGE]: {\n        width: 300,\n        height: 600\n    },\n    [AD_SLOTS.SUBNET_BANNER]: {\n        width: 468,\n        height: 60\n    },\n    [AD_SLOTS.SUBNET_WIDE_SKYSCRAPER]: {\n        width: 160,\n        height: 600\n    },\n    [AD_SLOTS.COMPANY_MEDIUM_RECTANGLE]: {\n        width: 300,\n        height: 250\n    },\n    [AD_SLOTS.COMPANY_HALF_PAGE]: {\n        width: 300,\n        height: 600\n    },\n    [AD_SLOTS.COMPANY_LEADERBOARD]: {\n        width: 728,\n        height: 90\n    },\n    [AD_SLOTS.COMPANY_SQUARE_BUTTON]: {\n        width: 125,\n        height: 125\n    },\n    [AD_SLOTS.GLOBAL_POPUP]: {\n        width: 400,\n        height: 400\n    },\n    [AD_SLOTS.GLOBAL_STICKY_FOOTER]: {\n        width: 320,\n        height: 50\n    }\n};\n// Google AdSense slots - each slot has a unique ID to avoid conflicts\nconst GOOGLE_AD_SLOTS = {\n    [AD_SLOTS.HOME_LEADERBOARD]: \"3369057847\",\n    [AD_SLOTS.HOME_BILLBOARD]: \"6182923442\",\n    [AD_SLOTS.HOME_MEDIUM_RECTANGLE]: \"7844510005\",\n    [AD_SLOTS.HOME_SKYSCRAPER]: \"5254383953\",\n    [AD_SLOTS.SUBNET_MEDIUM_RECTANGLE]: \"3064454441\",\n    [AD_SLOTS.SUBNET_HALF_PAGE]: \"9249469283\",\n    [AD_SLOTS.SUBNET_BANNER]: \"9765594162\",\n    [AD_SLOTS.SUBNET_WIDE_SKYSCRAPER]: \"6623305948\",\n    [AD_SLOTS.COMPANY_MEDIUM_RECTANGLE]: \"3997142602\",\n    [AD_SLOTS.COMPANY_HALF_PAGE]: \"8621384528\",\n    [AD_SLOTS.COMPANY_LEADERBOARD]: \"7308302852\",\n    [AD_SLOTS.COMPANY_SQUARE_BUTTON]: \"2378058731\",\n    [AD_SLOTS.GLOBAL_POPUP]: \"5445955649\",\n    [AD_SLOTS.GLOBAL_STICKY_FOOTER]: \"1064977062\"\n};\n/**\n * CLIENT-SIDE\n * Track ad impression\n */ async function trackAdImpression(impressionData) {\n    try {\n        const response = await fetch(\"/api/ads/track/impression\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(impressionData)\n        });\n        return response.ok;\n    } catch (error) {\n        console.error(\"Error tracking impression:\", error);\n        return false;\n    }\n}\n/**\n * CLIENT-SIDE\n * Track ad click and redirect\n */ async function trackAdClick(clickData) {\n    try {\n        const response = await fetch(\"/api/ads/track/click\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(clickData)\n        });\n        return response.ok;\n    } catch (error) {\n        console.error(\"Error tracking click:\", error);\n        return false;\n    }\n}\n/**\n * Generate a session ID for ad tracking\n */ function generateSessionId() {\n    // Generate a UUID v4 compatible string\n    return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(/[xy]/g, function(c) {\n        const r = Math.random() * 16 | 0;\n        const v = c == \"x\" ? r : r & 0x3 | 0x8;\n        return v.toString(16);\n    });\n}\n/**\n * Get device type from user agent\n */ function getDeviceType(userAgent) {\n    if (!userAgent) return \"unknown\";\n    const ua = userAgent.toLowerCase();\n    if (ua.includes(\"mobile\") || ua.includes(\"android\") || ua.includes(\"iphone\")) {\n        return \"mobile\";\n    }\n    if (ua.includes(\"tablet\") || ua.includes(\"ipad\")) {\n        return \"tablet\";\n    }\n    return \"desktop\";\n}\n/**\n * Get user's country code (placeholder - would integrate with geolocation service)\n */ function getCountryCode() {\n    // In a real implementation, this would use a geolocation service\n    // For now, return a default\n    return \"US\";\n}\n/**\n * Get user's language preference\n */ function getLanguage() {\n    if (true) {\n        return navigator.language || \"en-US\";\n    }\n    return \"en-US\";\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api/ad-serving.ts\n"));

/***/ })

});