"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/home-client-wrapper.tsx":
/*!*************************************!*\
  !*** ./app/home-client-wrapper.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomeClientWrapper)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ads_placements_smart_ad_banner__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ads-placements/smart-ad-banner */ \"(app-pages-browser)/./components/ads-placements/smart-ad-banner.tsx\");\n/* harmony import */ var _components_category_filter__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/category-filter */ \"(app-pages-browser)/./components/category-filter.tsx\");\n/* harmony import */ var _components_dashboard_dashboard_connect_widgets__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/dashboard/dashboard-connect-widgets */ \"(app-pages-browser)/./components/dashboard/dashboard-connect-widgets.tsx\");\n/* harmony import */ var _components_events_row__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/events-row */ \"(app-pages-browser)/./components/events-row.tsx\");\n/* harmony import */ var _components_featured_subnet_applications__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/featured-subnet-applications */ \"(app-pages-browser)/./components/featured-subnet-applications.tsx\");\n/* harmony import */ var _components_news_article_row__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/news-article-row */ \"(app-pages-browser)/./components/news-article-row.tsx\");\n/* harmony import */ var _components_news_section__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/news-section */ \"(app-pages-browser)/./components/news-section.tsx\");\n/* harmony import */ var _components_subnet_category_row__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/subnet-category-row */ \"(app-pages-browser)/./components/subnet-category-row.tsx\");\n/* harmony import */ var _components_trending_subnets__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/trending-subnets */ \"(app-pages-browser)/./components/trending-subnets.tsx\");\n/* harmony import */ var _components_trending_widgets__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/trending-widgets */ \"(app-pages-browser)/./components/trending-widgets.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_11__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction HomeClientWrapper(param) {\n    let { categories = [], subnets = [], metrics = [], networkStats = {}, companies = [], news = [], jobs = [], events = [], products = [] } = param;\n    _s();\n    const [selectedCategory_id, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_11__.useState)(null);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold mb-2\",\n                        children: \"Bittensor Subnet Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Explore and analyze the Bittensor ecosystem\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                lineNumber: 41,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full mb-8 flex justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ads_placements_smart_ad_banner__WEBPACK_IMPORTED_MODULE_1__.SmartAdBanner, {\n                    slotId: 1,\n                    googleAdSlot: \"3369057847\",\n                    className: \"mx-auto\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 5\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                lineNumber: 47,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_dashboard_connect_widgets__WEBPACK_IMPORTED_MODULE_3__.Dashboardspring, {\n                subnetsCount: subnets.length,\n                appsCount: products.length,\n                newsCount: news.length,\n                jobsCount: jobs.length,\n                eventsCount: events.length,\n                companiesCount: companies.length\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                lineNumber: 53,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold\",\n                                children: \"Trending Subnets\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 6\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"text-sm font-medium text-primary hover:underline\",\n                                onClick: ()=>window.location.href = \"/subnets\",\n                                children: \"View All\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 6\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-4 gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-3 bg-card dark:bg-card rounded-lg shadow-sm border dark:border-border\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 border-b dark:border-border\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-2 w-2 rounded-full bg-green-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                                                            lineNumber: 77,\n                                                            columnNumber: 10\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-medium\",\n                                                            children: \"Live Updates\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                                                            lineNumber: 78,\n                                                            columnNumber: 10\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                                                    lineNumber: 76,\n                                                    columnNumber: 9\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: \"Last updated: Just now\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 9\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 8\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_trending_subnets__WEBPACK_IMPORTED_MODULE_9__.TrendingSubnets, {\n                                            subnets: subnets,\n                                            metrics: metrics\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 8\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 7\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 6\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-card to-card/80 dark:from-card/90 dark:to-card/60 rounded-lg shadow-sm border dark:border-border overflow-hidden flex flex-col w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 border-b dark:border-border flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-xs\",\n                                                children: \"Featured\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 8\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-muted-foreground\",\n                                                children: \"Sponsored\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 8\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 7\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-h-[200px] w-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ads_placements_smart_ad_banner__WEBPACK_IMPORTED_MODULE_1__.SmartAdBanner, {\n                                            slotId: 3,\n                                            googleAdSlot: \"7844510005\",\n                                            className: \"w-full h-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 8\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 7\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 6\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                lineNumber: 62,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_events_row__WEBPACK_IMPORTED_MODULE_4__.EventsRow, {\n                    events: events,\n                    company: companies\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 5\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                lineNumber: 99,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold mb-3\",\n                        children: \"Explore Subnets by Category\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_category_filter__WEBPACK_IMPORTED_MODULE_2__.CategoryFilter, {\n                        selectedCategory_id: selectedCategory_id,\n                        onSelectCategory: setSelectedCategory,\n                        categories: categories\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                lineNumber: 103,\n                columnNumber: 4\n            }, this),\n            selectedCategory_id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-8\",\n                children: [\n                    categories.filter((category)=>category.id === selectedCategory_id).map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_subnet_category_row__WEBPACK_IMPORTED_MODULE_8__.SubnetCategoryRow, {\n                            categoryName: category.name,\n                            categoryId: category.id,\n                            subnets: subnets.filter((s)=>{\n                                var _category_netuids;\n                                return (_category_netuids = category.netuids) === null || _category_netuids === void 0 ? void 0 : _category_netuids.includes(s.netuid);\n                            }),\n                            metrics: metrics,\n                            isScrollable: false\n                        }, category.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 8\n                        }, this)),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold mb-3\",\n                                children: \"Latest Subnet News\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 7\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_news_article_row__WEBPACK_IMPORTED_MODULE_6__.NewsArticleRow, {\n                                news: news,\n                                subnets: subnets,\n                                categories: categories,\n                                companies: companies\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 7\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 6\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                lineNumber: 113,\n                columnNumber: 5\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-8\",\n                children: [\n                    categories.slice(0, 2).map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_subnet_category_row__WEBPACK_IMPORTED_MODULE_8__.SubnetCategoryRow, {\n                            categoryName: category.name,\n                            categoryId: category.id,\n                            subnets: subnets.filter((s)=>{\n                                var _category_netuids;\n                                return (_category_netuids = category.netuids) === null || _category_netuids === void 0 ? void 0 : _category_netuids.includes(s.netuid);\n                            }),\n                            metrics: metrics,\n                            isScrollable: true\n                        }, category.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 7\n                        }, this)),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold mb-3\",\n                                children: \"Latest Subnet News\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 7\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_news_article_row__WEBPACK_IMPORTED_MODULE_6__.NewsArticleRow, {\n                                news: news,\n                                subnets: subnets,\n                                categories: categories,\n                                companies: companies\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 7\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 6\n                    }, this),\n                    categories.slice(2).map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_subnet_category_row__WEBPACK_IMPORTED_MODULE_8__.SubnetCategoryRow, {\n                            categoryName: category.name,\n                            categoryId: category.id,\n                            subnets: subnets.filter((s)=>{\n                                var _category_netuids;\n                                return (_category_netuids = category.netuids) === null || _category_netuids === void 0 ? void 0 : _category_netuids.includes(s.netuid);\n                            }),\n                            metrics: metrics,\n                            isScrollable: true\n                        }, category.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 7\n                        }, this))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                lineNumber: 133,\n                columnNumber: 5\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full mt-8 mb-8 flex justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ads_placements_smart_ad_banner__WEBPACK_IMPORTED_MODULE_1__.SmartAdBanner, {\n                    slotId: 2,\n                    googleAdSlot: \"7230250579\",\n                    className: \"mx-auto\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 5\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                lineNumber: 163,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_featured_subnet_applications__WEBPACK_IMPORTED_MODULE_5__.FeaturedSubnetApplications, {\n                    apps: products,\n                    categories: categories\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 5\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                lineNumber: 167,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6 mt-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_news_section__WEBPACK_IMPORTED_MODULE_7__.NewsSection, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_trending_widgets__WEBPACK_IMPORTED_MODULE_10__.TrendingWidgets, {\n                                subnets: subnets,\n                                subnetMetrics: metrics,\n                                networkStats: networkStats\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 6\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ads_placements_smart_ad_banner__WEBPACK_IMPORTED_MODULE_1__.SmartAdBanner, {\n                                    slotId: 4\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 7\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 6\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\app\\\\home-client-wrapper.tsx\",\n                lineNumber: 171,\n                columnNumber: 4\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(HomeClientWrapper, \"TKAA+6IQJtXgKiOlUFIYLi7KAXo=\");\n_c = HomeClientWrapper;\nvar _c;\n$RefreshReg$(_c, \"HomeClientWrapper\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/home-client-wrapper.tsx\n"));

/***/ })

});