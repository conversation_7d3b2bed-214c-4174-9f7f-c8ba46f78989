"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/subnets/[id]/page",{

/***/ "(app-pages-browser)/./components/ads-placements/smart-ad-banner.tsx":
/*!*******************************************************!*\
  !*** ./components/ads-placements/smart-ad-banner.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SmartAdBanner: () => (/* binding */ SmartAdBanner)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _lib_ad_serving__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/ad-serving */ \"(app-pages-browser)/./lib/ad-serving.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ SmartAdBanner auto */ \nvar _s = $RefreshSig$();\n\n\n// Simple logging functions for ad performance tracking\nconst logAdPerformance = (data)=>{\n    if (true) {\n        console.log(\"Ad Performance:\", data);\n    }\n};\nconst logAdError = (data)=>{\n    if (true) {\n        console.error(\"Ad Error:\", data);\n    }\n};\nfunction SmartAdBanner(param) {\n    let { slotId, className, googleAdSlot, enablePaidAds = true } = param;\n    _s();\n    const [paidAd, setPaidAd] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [retryCount, setRetryCount] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [sessionId] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        \"SmartAdBanner.useState\": ()=>(0,_lib_ad_serving__WEBPACK_IMPORTED_MODULE_1__.generateSessionId)()\n    }[\"SmartAdBanner.useState\"]);\n    const [hasTrackedImpression, setHasTrackedImpression] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [googleAdError, setGoogleAdError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [fallbackMode, setFallbackMode] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"none\");\n    const [loadStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        \"SmartAdBanner.useState\": ()=>Date.now()\n    }[\"SmartAdBanner.useState\"]);\n    const adContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const googleAdInitialized = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(false);\n    const googleAdLoadTimeout = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const maxRetries = 3;\n    // Get dimensions for this slot\n    const dimensions = _lib_ad_serving__WEBPACK_IMPORTED_MODULE_1__.SLOT_DIMENSIONS[slotId];\n    if (!dimensions) {\n        console.error(\"No dimensions found for slot ID: \".concat(slotId));\n        return null;\n    }\n    // Fetch paid ad on component mount with retry logic\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"SmartAdBanner.useEffect\": ()=>{\n            if (!enablePaidAds) {\n                setIsLoading(false);\n                return;\n            }\n            const fetchPaidAd = {\n                \"SmartAdBanner.useEffect.fetchPaidAd\": async function() {\n                    let attempt = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0;\n                    try {\n                        setError(null);\n                        const userContext = {\n                            country_code: (0,_lib_ad_serving__WEBPACK_IMPORTED_MODULE_1__.getCountryCode)(),\n                            device_type: (0,_lib_ad_serving__WEBPACK_IMPORTED_MODULE_1__.getDeviceType)(navigator.userAgent),\n                            language: (0,_lib_ad_serving__WEBPACK_IMPORTED_MODULE_1__.getLanguage)(),\n                            user_agent: navigator.userAgent\n                        };\n                        console.log(\"[Ad Serving] Fetching paid ad for slot \".concat(slotId), userContext);\n                        const controller = new AbortController();\n                        const timeoutId = setTimeout({\n                            \"SmartAdBanner.useEffect.fetchPaidAd.timeoutId\": ()=>{\n                                console.log(\"[Ad Serving] Request timeout for slot \".concat(slotId));\n                                controller.abort();\n                            }\n                        }[\"SmartAdBanner.useEffect.fetchPaidAd.timeoutId\"], 10000); // Increased to 10 second timeout\n                        const response = await fetch(\"/api/ads/serve\", {\n                            method: \"POST\",\n                            headers: {\n                                \"Content-Type\": \"application/json\"\n                            },\n                            body: JSON.stringify({\n                                slotId,\n                                ...userContext\n                            }),\n                            signal: controller.signal\n                        });\n                        clearTimeout(timeoutId);\n                        console.log(\"[Ad Serving] Response received for slot \".concat(slotId, \":\"), response.status, response.statusText);\n                        if (response.ok) {\n                            const result = await response.json();\n                            if (result.success && result.data) {\n                                setPaidAd(result.data);\n                                setRetryCount(0);\n                                // Log successful paid ad load\n                                logAdPerformance({\n                                    slotId,\n                                    paidAdSuccess: true,\n                                    adsenseSuccess: false,\n                                    loadTime: Date.now() - loadStartTime,\n                                    fallbackUsed: false\n                                });\n                                return;\n                            }\n                        }\n                        // If we get here, no paid ads were available (404) or other non-critical error\n                        if (response.status === 404) {\n                            // No ads available - this is expected, fall back to Google\n                            setRetryCount(0);\n                            return;\n                        }\n                        throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n                    } catch (error) {\n                        console.error(\"[Ad Serving] Error fetching paid ad for slot \".concat(slotId, \" (attempt \").concat(attempt + 1, \"):\"), error);\n                        // Log the error\n                        logAdError({\n                            slotId,\n                            errorType: error instanceof Error && error.name === \"AbortError\" ? \"timeout\" : error instanceof Error && error.message.includes(\"timeout\") ? \"timeout\" : \"paid_ad_failure\",\n                            errorMessage: error instanceof Error ? error.message : \"Unknown error\",\n                            retryCount: attempt + 1,\n                            sessionId\n                        });\n                        if (attempt < maxRetries - 1) {\n                            // Exponential backoff: 1s, 2s, 4s\n                            const delay = Math.pow(2, attempt) * 1000;\n                            setTimeout({\n                                \"SmartAdBanner.useEffect.fetchPaidAd\": ()=>{\n                                    setRetryCount(attempt + 1);\n                                    fetchPaidAd(attempt + 1);\n                                }\n                            }[\"SmartAdBanner.useEffect.fetchPaidAd\"], delay);\n                        } else {\n                            setError(\"Failed to load paid ads after multiple attempts\");\n                            setRetryCount(0);\n                            setFallbackMode(\"google\");\n                        }\n                    } finally{\n                        if (attempt === 0 || attempt >= maxRetries - 1) {\n                            setIsLoading(false);\n                        }\n                    }\n                }\n            }[\"SmartAdBanner.useEffect.fetchPaidAd\"];\n            fetchPaidAd();\n        }\n    }[\"SmartAdBanner.useEffect\"], [\n        slotId,\n        enablePaidAds,\n        maxRetries\n    ]);\n    // Track impression when ad becomes visible with error handling\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"SmartAdBanner.useEffect\": ()=>{\n            if (!paidAd || hasTrackedImpression) return;\n            const observer = new IntersectionObserver({\n                \"SmartAdBanner.useEffect\": (entries)=>{\n                    entries.forEach({\n                        \"SmartAdBanner.useEffect\": (entry)=>{\n                            if (entry.isIntersecting && entry.intersectionRatio > 0.5) {\n                                (0,_lib_ad_serving__WEBPACK_IMPORTED_MODULE_1__.trackAdImpression)({\n                                    ad_id: paidAd.id,\n                                    session_id: sessionId,\n                                    country_code: (0,_lib_ad_serving__WEBPACK_IMPORTED_MODULE_1__.getCountryCode)(),\n                                    device_type: (0,_lib_ad_serving__WEBPACK_IMPORTED_MODULE_1__.getDeviceType)(navigator.userAgent)\n                                }).catch({\n                                    \"SmartAdBanner.useEffect\": (error)=>{\n                                        console.error(\"Failed to track impression:\", error);\n                                    // Don't block the user experience for tracking failures\n                                    }\n                                }[\"SmartAdBanner.useEffect\"]);\n                                setHasTrackedImpression(true);\n                                observer.disconnect();\n                            }\n                        }\n                    }[\"SmartAdBanner.useEffect\"]);\n                }\n            }[\"SmartAdBanner.useEffect\"], {\n                threshold: 0.5\n            });\n            if (adContainerRef.current) {\n                observer.observe(adContainerRef.current);\n            }\n            return ({\n                \"SmartAdBanner.useEffect\": ()=>observer.disconnect()\n            })[\"SmartAdBanner.useEffect\"];\n        }\n    }[\"SmartAdBanner.useEffect\"], [\n        paidAd,\n        sessionId,\n        hasTrackedImpression\n    ]);\n    // Initialize Google AdSense when no paid ad is available\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"SmartAdBanner.useEffect\": ()=>{\n            if (isLoading || paidAd || googleAdInitialized.current) return;\n            const container = adContainerRef.current;\n            const adEl = container === null || container === void 0 ? void 0 : container.querySelector(\"ins.adsbygoogle\");\n            if (!adEl) return;\n            // Clear any previous ad content\n            adEl.innerHTML = \"\";\n            const { width, height } = dimensions;\n            // Apply sizing rules based on slot type\n            const isHorizontal = width > height; // Leaderboard, Billboard, Banner types\n            if (isHorizontal && container) {\n                var _container_parentElement;\n                const parentWidth = ((_container_parentElement = container.parentElement) === null || _container_parentElement === void 0 ? void 0 : _container_parentElement.clientWidth) || width;\n                const calculatedWidth = Math.min(parentWidth, width);\n                const calculatedHeight = height / width * calculatedWidth;\n                container.style.width = \"\".concat(calculatedWidth, \"px\");\n                container.style.height = \"\".concat(calculatedHeight, \"px\");\n            } else if (container) {\n                container.style.width = \"\".concat(width, \"px\");\n                container.style.height = \"\".concat(height, \"px\");\n            }\n            if (container) {\n                container.style.overflow = \"hidden\";\n                container.style.position = \"relative\";\n            }\n            // Enhanced Google AdSense loading with comprehensive error handling\n            let googleRetryCount = 0;\n            const loadGoogleAd = {\n                \"SmartAdBanner.useEffect.loadGoogleAd\": ()=>{\n                    try {\n                        // Clear any existing timeout\n                        if (googleAdLoadTimeout.current) {\n                            clearTimeout(googleAdLoadTimeout.current);\n                        }\n                        // Set timeout for Google AdSense loading\n                        googleAdLoadTimeout.current = setTimeout({\n                            \"SmartAdBanner.useEffect.loadGoogleAd\": ()=>{\n                                if (!googleAdInitialized.current) {\n                                    console.error(\"Google AdSense load timeout for slot:\", slotId);\n                                    setGoogleAdError(\"AdSense load timeout\");\n                                    setFallbackMode(\"placeholder\");\n                                    // Log AdSense timeout\n                                    logAdError({\n                                        slotId,\n                                        errorType: \"timeout\",\n                                        errorMessage: \"Google AdSense load timeout\",\n                                        sessionId,\n                                        fallbackUsed: \"placeholder\"\n                                    });\n                                }\n                            }\n                        }[\"SmartAdBanner.useEffect.loadGoogleAd\"], 10000); // 10 second timeout\n                        (window.adsbygoogle = window.adsbygoogle || []).push({});\n                        googleAdInitialized.current = true;\n                        setFallbackMode(\"google\");\n                        // Clear timeout on successful load\n                        if (googleAdLoadTimeout.current) {\n                            clearTimeout(googleAdLoadTimeout.current);\n                        }\n                        // Log successful AdSense load\n                        logAdPerformance({\n                            slotId,\n                            paidAdSuccess: false,\n                            adsenseSuccess: true,\n                            loadTime: Date.now() - loadStartTime,\n                            fallbackUsed: true\n                        });\n                    } catch (e) {\n                        googleRetryCount++;\n                        console.error(\"Google AdSense load error (attempt \".concat(googleRetryCount, \"):\"), e);\n                        if (googleRetryCount < 3) {\n                            setTimeout(loadGoogleAd, 1000 * googleRetryCount); // Progressive delay\n                        } else {\n                            console.error(\"Google AdSense load failed after 3 attempts for slot:\", slotId);\n                            const errorMessage = \"AdSense failed: \".concat(e instanceof Error ? e.message : \"Unknown error\");\n                            setGoogleAdError(errorMessage);\n                            setFallbackMode(\"placeholder\");\n                            // Log AdSense failure\n                            logAdError({\n                                slotId,\n                                errorType: \"adsense_failure\",\n                                errorMessage,\n                                retryCount: googleRetryCount,\n                                sessionId,\n                                fallbackUsed: \"placeholder\"\n                            });\n                        }\n                    }\n                }\n            }[\"SmartAdBanner.useEffect.loadGoogleAd\"];\n            // Only load Google ads if we don't have a paid ad and no placeholder slot\n            if (!googleAdSlot.includes(\"PLACEHOLDER\")) {\n                loadGoogleAd();\n            } else {\n                console.warn(\"Placeholder Google AdSense slot detected for slotId \".concat(slotId, \": \").concat(googleAdSlot));\n                setFallbackMode(\"placeholder\");\n            }\n            return ({\n                \"SmartAdBanner.useEffect\": ()=>{\n                    if (adEl) adEl.innerHTML = \"\";\n                }\n            })[\"SmartAdBanner.useEffect\"];\n        }\n    }[\"SmartAdBanner.useEffect\"], [\n        slotId,\n        isLoading,\n        paidAd\n    ]);\n    // Handle paid ad click with error handling\n    const handlePaidAdClick = async ()=>{\n        if (!paidAd) return;\n        try {\n            // Track the click (don't wait for it to complete)\n            (0,_lib_ad_serving__WEBPACK_IMPORTED_MODULE_1__.trackAdClick)({\n                ad_id: paidAd.id,\n                session_id: sessionId,\n                country_code: (0,_lib_ad_serving__WEBPACK_IMPORTED_MODULE_1__.getCountryCode)(),\n                device_type: (0,_lib_ad_serving__WEBPACK_IMPORTED_MODULE_1__.getDeviceType)(navigator.userAgent)\n            }).catch((error)=>{\n                console.error(\"Failed to track click:\", error);\n            // Don't block the redirect for tracking failures\n            });\n            // Redirect to target URL immediately\n            window.open(paidAd.target_url, \"_blank\", \"noopener,noreferrer\");\n        } catch (error) {\n            console.error(\"Error handling ad click:\", error);\n            // Still try to redirect even if tracking fails\n            window.open(paidAd.target_url, \"_blank\", \"noopener,noreferrer\");\n        }\n    };\n    // Validate that the required Google AdSense slot is provided\n    if (!googleAdSlot || googleAdSlot.includes(\"PLACEHOLDER\")) {\n        console.error(\"Invalid Google AdSense slot for slotId \".concat(slotId, \": \").concat(googleAdSlot));\n    }\n    // Render fallback placeholder when both paid ads and AdSense fail\n    const renderFallbackPlaceholder = ()=>{\n        const { width, height } = dimensions;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"inline-block \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: \"\".concat(width, \"px\"),\n                    height: \"\".concat(height, \"px\"),\n                    margin: \"0 auto\",\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\",\n                    backgroundColor: \"#f8fafc\",\n                    border: \"2px dashed #e2e8f0\",\n                    borderRadius: \"8px\",\n                    color: \"#64748b\",\n                    fontSize: \"14px\",\n                    textAlign: \"center\",\n                    padding: \"16px\",\n                    boxSizing: \"border-box\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginBottom: \"8px\",\n                            fontSize: \"16px\"\n                        },\n                        children: \"\\uD83D\\uDCE2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                        lineNumber: 368,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontWeight: \"500\",\n                            marginBottom: \"4px\"\n                        },\n                        children: \"Advertisement Space\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                        lineNumber: 369,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"12px\",\n                            opacity: 0.7\n                        },\n                        children: error && googleAdError ? \"Service temporarily unavailable\" : googleAdSlot.includes(\"PLACEHOLDER\") ? \"Slot configuration pending\" : \"Loading advertisement...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 6\n                    }, this),\n                     true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"10px\",\n                            marginTop: \"8px\",\n                            opacity: 0.5\n                        },\n                        children: [\n                            \"Slot \",\n                            slotId,\n                            \" • \",\n                            width,\n                            \"\\xd7\",\n                            height\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                        lineNumber: 378,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                lineNumber: 349,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n            lineNumber: 348,\n            columnNumber: 4\n        }, this);\n    };\n    // Show loading state\n    if (isLoading && enablePaidAds) {\n        const { width, height } = dimensions;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"inline-block \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: \"\".concat(width, \"px\"),\n                    height: \"\".concat(height, \"px\"),\n                    margin: \"0 auto\",\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\",\n                    backgroundColor: \"#f3f4f6\",\n                    border: \"1px solid #e5e7eb\",\n                    borderRadius: \"4px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm text-gray-500\",\n                    children: retryCount > 0 ? \"Loading ads... (\".concat(retryCount, \"/\").concat(maxRetries, \")\") : \"Loading ads...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                    lineNumber: 405,\n                    columnNumber: 6\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                lineNumber: 392,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n            lineNumber: 391,\n            columnNumber: 4\n        }, this);\n    }\n    // Show fallback placeholder when both paid ads and AdSense fail\n    if (fallbackMode === \"placeholder\") {\n        return renderFallbackPlaceholder();\n    }\n    // Show error state (only if we have an error and no fallback)\n    if (error && !enablePaidAds && fallbackMode === \"none\") {\n        return renderFallbackPlaceholder();\n    }\n    // Render paid ad\n    if (paidAd) {\n        const { width, height } = dimensions;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"inline-block \".concat(className),\n            ref: adContainerRef,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: \"\".concat(width, \"px\"),\n                    height: \"\".concat(height, \"px\"),\n                    margin: \"0 auto\",\n                    position: \"relative\",\n                    cursor: \"pointer\",\n                    overflow: \"hidden\",\n                    border: \"1px solid #e5e7eb\",\n                    borderRadius: \"4px\"\n                },\n                onClick: handlePaidAdClick,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: paidAd.image_url,\n                        alt: paidAd.title,\n                        style: {\n                            width: \"100%\",\n                            height: \"100%\",\n                            objectFit: \"cover\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                        lineNumber: 442,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"absolute\",\n                            top: \"4px\",\n                            right: \"4px\",\n                            background: \"rgba(0,0,0,0.7)\",\n                            color: \"white\",\n                            fontSize: \"10px\",\n                            padding: \"2px 4px\",\n                            borderRadius: \"2px\"\n                        },\n                        children: \"Sponsored\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                        lineNumber: 451,\n                        columnNumber: 6\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                lineNumber: 429,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n            lineNumber: 428,\n            columnNumber: 4\n        }, this);\n    }\n    // Render Google AdSense fallback (only if not in placeholder mode)\n    if (fallbackMode === \"google\" || !error && !googleAdError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"inline-block \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: adContainerRef,\n                style: {\n                    minWidth: \"\".concat(dimensions.width, \"px\"),\n                    minHeight: \"\".concat(dimensions.height, \"px\"),\n                    margin: \"0 auto\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ins\", {\n                    className: \"adsbygoogle\",\n                    style: {\n                        display: \"block\"\n                    },\n                    \"data-ad-client\": \"ca-pub-5681407322305640\",\n                    \"data-ad-slot\": googleAdSlot,\n                    \"data-ad-format\": dimensions.width > dimensions.height ? \"horizontal\" : dimensions.width === dimensions.height ? \"rectangle\" : \"auto\",\n                    \"data-full-width-responsive\": \"true\"\n                }, \"\".concat(slotId, \"-\").concat(googleAdSlot), false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                    lineNumber: 482,\n                    columnNumber: 6\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                lineNumber: 474,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n            lineNumber: 473,\n            columnNumber: 4\n        }, this);\n    }\n    // Final fallback - render placeholder\n    return renderFallbackPlaceholder();\n}\n_s(SmartAdBanner, \"1JBpCQtIfTae8lbGZzGSXCQ6b20=\");\n_c = SmartAdBanner;\nvar _c;\n$RefreshReg$(_c, \"SmartAdBanner\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ads-placements/smart-ad-banner.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/ad-serving.ts":
/*!***************************!*\
  !*** ./lib/ad-serving.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AD_SLOTS: () => (/* binding */ AD_SLOTS),\n/* harmony export */   GOOGLE_AD_SLOTS: () => (/* binding */ GOOGLE_AD_SLOTS),\n/* harmony export */   SLOT_DIMENSIONS: () => (/* binding */ SLOT_DIMENSIONS),\n/* harmony export */   generateSessionId: () => (/* binding */ generateSessionId),\n/* harmony export */   getCountryCode: () => (/* binding */ getCountryCode),\n/* harmony export */   getDeviceType: () => (/* binding */ getDeviceType),\n/* harmony export */   getLanguage: () => (/* binding */ getLanguage),\n/* harmony export */   trackAdClick: () => (/* binding */ trackAdClick),\n/* harmony export */   trackAdImpression: () => (/* binding */ trackAdImpression)\n/* harmony export */ });\n// Database ad slot IDs and their configurations\nconst AD_SLOTS = {\n    // Home Page Slots (1-4)\n    HOME_LEADERBOARD: 1,\n    HOME_BILLBOARD: 2,\n    HOME_MEDIUM_RECTANGLE: 3,\n    HOME_SKYSCRAPER: 4,\n    // Subnet Page Slots (7-10)\n    SUBNET_MEDIUM_RECTANGLE: 7,\n    SUBNET_HALF_PAGE: 8,\n    SUBNET_BANNER: 9,\n    SUBNET_WIDE_SKYSCRAPER: 10,\n    // Company Page Slots (11-14)\n    COMPANY_MEDIUM_RECTANGLE: 11,\n    COMPANY_HALF_PAGE: 12,\n    COMPANY_LEADERBOARD: 13,\n    COMPANY_SQUARE_BUTTON: 14,\n    // Global Slots (15-16)\n    GLOBAL_POPUP: 15,\n    GLOBAL_STICKY_FOOTER: 16\n};\n// Slot dimensions mapping\nconst SLOT_DIMENSIONS = {\n    [AD_SLOTS.HOME_LEADERBOARD]: {\n        width: 728,\n        height: 90\n    },\n    [AD_SLOTS.HOME_BILLBOARD]: {\n        width: 970,\n        height: 250\n    },\n    [AD_SLOTS.HOME_MEDIUM_RECTANGLE]: {\n        width: 300,\n        height: 250\n    },\n    [AD_SLOTS.HOME_SKYSCRAPER]: {\n        width: 160,\n        height: 600\n    },\n    [AD_SLOTS.SUBNET_MEDIUM_RECTANGLE]: {\n        width: 300,\n        height: 250\n    },\n    [AD_SLOTS.SUBNET_HALF_PAGE]: {\n        width: 300,\n        height: 600\n    },\n    [AD_SLOTS.SUBNET_BANNER]: {\n        width: 468,\n        height: 60\n    },\n    [AD_SLOTS.SUBNET_WIDE_SKYSCRAPER]: {\n        width: 160,\n        height: 600\n    },\n    [AD_SLOTS.COMPANY_MEDIUM_RECTANGLE]: {\n        width: 300,\n        height: 250\n    },\n    [AD_SLOTS.COMPANY_HALF_PAGE]: {\n        width: 300,\n        height: 600\n    },\n    [AD_SLOTS.COMPANY_LEADERBOARD]: {\n        width: 728,\n        height: 90\n    },\n    [AD_SLOTS.COMPANY_SQUARE_BUTTON]: {\n        width: 125,\n        height: 125\n    },\n    [AD_SLOTS.GLOBAL_POPUP]: {\n        width: 400,\n        height: 400\n    },\n    [AD_SLOTS.GLOBAL_STICKY_FOOTER]: {\n        width: 320,\n        height: 50\n    }\n};\n// Google AdSense slots - each slot has a unique ID to avoid conflicts\nconst GOOGLE_AD_SLOTS = {\n    [AD_SLOTS.HOME_LEADERBOARD]: \"3369057847\",\n    [AD_SLOTS.HOME_BILLBOARD]: \"6182923442\",\n    [AD_SLOTS.HOME_MEDIUM_RECTANGLE]: \"7844510005\",\n    [AD_SLOTS.HOME_SKYSCRAPER]: \"5254383953\",\n    [AD_SLOTS.SUBNET_MEDIUM_RECTANGLE]: \"3064454441\",\n    [AD_SLOTS.SUBNET_HALF_PAGE]: \"9249469283\",\n    [AD_SLOTS.SUBNET_BANNER]: \"9765594162\",\n    [AD_SLOTS.SUBNET_WIDE_SKYSCRAPER]: \"6623305948\",\n    [AD_SLOTS.COMPANY_MEDIUM_RECTANGLE]: \"3997142602\",\n    [AD_SLOTS.COMPANY_HALF_PAGE]: \"8621384528\",\n    [AD_SLOTS.COMPANY_LEADERBOARD]: \"7308302852\",\n    [AD_SLOTS.COMPANY_SQUARE_BUTTON]: \"2378058731\",\n    [AD_SLOTS.GLOBAL_POPUP]: \"5445955649\",\n    [AD_SLOTS.GLOBAL_STICKY_FOOTER]: \"1064977062\"\n};\n/**\n * CLIENT-SIDE\n * Track ad impression\n */ async function trackAdImpression(impressionData) {\n    try {\n        const response = await fetch(\"/api/ads/track/impression\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(impressionData)\n        });\n        return response.ok;\n    } catch (error) {\n        console.error(\"Error tracking impression:\", error);\n        return false;\n    }\n}\n/**\n * CLIENT-SIDE\n * Track ad click and redirect\n */ async function trackAdClick(clickData) {\n    try {\n        const response = await fetch(\"/api/ads/track/click\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(clickData)\n        });\n        return response.ok;\n    } catch (error) {\n        console.error(\"Error tracking click:\", error);\n        return false;\n    }\n}\n/**\n * Generate a session ID for ad tracking\n */ function generateSessionId() {\n    // Generate a UUID v4 compatible string\n    return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(/[xy]/g, function(c) {\n        const r = Math.random() * 16 | 0;\n        const v = c == \"x\" ? r : r & 0x3 | 0x8;\n        return v.toString(16);\n    });\n}\n/**\n * Get device type from user agent\n */ function getDeviceType(userAgent) {\n    if (!userAgent) return \"unknown\";\n    const ua = userAgent.toLowerCase();\n    if (ua.includes(\"mobile\") || ua.includes(\"android\") || ua.includes(\"iphone\")) {\n        return \"mobile\";\n    }\n    if (ua.includes(\"tablet\") || ua.includes(\"ipad\")) {\n        return \"tablet\";\n    }\n    return \"desktop\";\n}\n/**\n * Get user's country code (placeholder - would integrate with geolocation service)\n */ function getCountryCode() {\n    // In a real implementation, this would use a geolocation service\n    // For now, return a default\n    return \"US\";\n}\n/**\n * Get user's language preference\n */ function getLanguage() {\n    if (true) {\n        return navigator.language || \"en-US\";\n    }\n    return \"en-US\";\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/ad-serving.ts\n"));

/***/ })

});