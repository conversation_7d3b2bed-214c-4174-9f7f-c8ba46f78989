"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/subnets/[id]/page",{

/***/ "(app-pages-browser)/./components/ads-placements/smart-ad-banner.tsx":
/*!*******************************************************!*\
  !*** ./components/ads-placements/smart-ad-banner.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SmartAdBanner: () => (/* binding */ SmartAdBanner)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/api/ad-serving */ \"(app-pages-browser)/./lib/api/ad-serving.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ SmartAdBanner auto */ \nvar _s = $RefreshSig$();\n\n\n// Simple logging functions for ad performance tracking\nconst logAdPerformance = (data)=>{\n    if (true) {\n        console.log(\"Ad Performance:\", data);\n    }\n};\nconst logAdError = (data)=>{\n    if (true) {\n        console.error(\"Ad Error:\", data);\n    }\n};\nfunction SmartAdBanner(param) {\n    let { slotId, className, googleAdSlot, enablePaidAds = true } = param;\n    _s();\n    const [paidAd, setPaidAd] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [retryCount, setRetryCount] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [sessionId] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        \"SmartAdBanner.useState\": ()=>(0,_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.generateSessionId)()\n    }[\"SmartAdBanner.useState\"]);\n    const [hasTrackedImpression, setHasTrackedImpression] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [googleAdError, setGoogleAdError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [fallbackMode, setFallbackMode] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"none\");\n    const [loadStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        \"SmartAdBanner.useState\": ()=>Date.now()\n    }[\"SmartAdBanner.useState\"]);\n    const adContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const googleAdInitialized = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(false);\n    const googleAdLoadTimeout = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const maxRetries = 3;\n    // Get dimensions for this slot\n    const dimensions = _lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.SLOT_DIMENSIONS[slotId];\n    if (!dimensions) {\n        console.error(\"No dimensions found for slot ID: \".concat(slotId));\n        return null;\n    }\n    // Fetch paid ad on component mount with retry logic\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"SmartAdBanner.useEffect\": ()=>{\n            if (!enablePaidAds) {\n                setIsLoading(false);\n                return;\n            }\n            const fetchPaidAd = {\n                \"SmartAdBanner.useEffect.fetchPaidAd\": async function() {\n                    let attempt = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0;\n                    try {\n                        setError(null);\n                        const userContext = {\n                            country_code: (0,_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.getCountryCode)(),\n                            device_type: (0,_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.getDeviceType)(navigator.userAgent),\n                            language: (0,_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.getLanguage)(),\n                            user_agent: navigator.userAgent\n                        };\n                        console.log(\"[Ad Serving] Fetching paid ad for slot \".concat(slotId), userContext);\n                        const controller = new AbortController();\n                        const timeoutId = setTimeout({\n                            \"SmartAdBanner.useEffect.fetchPaidAd.timeoutId\": ()=>{\n                                console.log(\"[Ad Serving] Request timeout for slot \".concat(slotId));\n                                controller.abort();\n                            }\n                        }[\"SmartAdBanner.useEffect.fetchPaidAd.timeoutId\"], 10000); // Increased to 10 second timeout\n                        const response = await fetch(\"/api/ads/serve\", {\n                            method: \"POST\",\n                            headers: {\n                                \"Content-Type\": \"application/json\"\n                            },\n                            body: JSON.stringify({\n                                slotId,\n                                ...userContext\n                            }),\n                            signal: controller.signal\n                        });\n                        clearTimeout(timeoutId);\n                        console.log(\"[Ad Serving] Response received for slot \".concat(slotId, \":\"), response.status, response.statusText);\n                        if (response.ok) {\n                            const result = await response.json();\n                            if (result.success && result.data) {\n                                setPaidAd(result.data);\n                                setRetryCount(0);\n                                // Log successful paid ad load\n                                logAdPerformance({\n                                    slotId,\n                                    paidAdSuccess: true,\n                                    adsenseSuccess: false,\n                                    loadTime: Date.now() - loadStartTime,\n                                    fallbackUsed: false\n                                });\n                                return;\n                            }\n                        }\n                        // If we get here, no paid ads were available (404) or other non-critical error\n                        if (response.status === 404) {\n                            // No ads available - this is expected, fall back to Google\n                            setRetryCount(0);\n                            return;\n                        }\n                        throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n                    } catch (error) {\n                        console.error(\"Error fetching paid ad (attempt \".concat(attempt + 1, \"):\"), error);\n                        // Log the error\n                        logAdError({\n                            slotId,\n                            errorType: error instanceof Error && error.message.includes(\"timeout\") ? \"timeout\" : \"paid_ad_failure\",\n                            errorMessage: error instanceof Error ? error.message : \"Unknown error\",\n                            retryCount: attempt + 1,\n                            sessionId\n                        });\n                        if (attempt < maxRetries - 1) {\n                            // Exponential backoff: 1s, 2s, 4s\n                            const delay = Math.pow(2, attempt) * 1000;\n                            setTimeout({\n                                \"SmartAdBanner.useEffect.fetchPaidAd\": ()=>{\n                                    setRetryCount(attempt + 1);\n                                    fetchPaidAd(attempt + 1);\n                                }\n                            }[\"SmartAdBanner.useEffect.fetchPaidAd\"], delay);\n                        } else {\n                            setError(\"Failed to load paid ads after multiple attempts\");\n                            setRetryCount(0);\n                            setFallbackMode(\"google\");\n                        }\n                    } finally{\n                        if (attempt === 0 || attempt >= maxRetries - 1) {\n                            setIsLoading(false);\n                        }\n                    }\n                }\n            }[\"SmartAdBanner.useEffect.fetchPaidAd\"];\n            fetchPaidAd();\n        }\n    }[\"SmartAdBanner.useEffect\"], [\n        slotId,\n        enablePaidAds,\n        maxRetries\n    ]);\n    // Track impression when ad becomes visible with error handling\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"SmartAdBanner.useEffect\": ()=>{\n            if (!paidAd || hasTrackedImpression) return;\n            const observer = new IntersectionObserver({\n                \"SmartAdBanner.useEffect\": (entries)=>{\n                    entries.forEach({\n                        \"SmartAdBanner.useEffect\": (entry)=>{\n                            if (entry.isIntersecting && entry.intersectionRatio > 0.5) {\n                                (0,_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.trackAdImpression)({\n                                    ad_id: paidAd.id,\n                                    session_id: sessionId,\n                                    country_code: (0,_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.getCountryCode)(),\n                                    device_type: (0,_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.getDeviceType)(navigator.userAgent)\n                                }).catch({\n                                    \"SmartAdBanner.useEffect\": (error)=>{\n                                        console.error(\"Failed to track impression:\", error);\n                                    // Don't block the user experience for tracking failures\n                                    }\n                                }[\"SmartAdBanner.useEffect\"]);\n                                setHasTrackedImpression(true);\n                                observer.disconnect();\n                            }\n                        }\n                    }[\"SmartAdBanner.useEffect\"]);\n                }\n            }[\"SmartAdBanner.useEffect\"], {\n                threshold: 0.5\n            });\n            if (adContainerRef.current) {\n                observer.observe(adContainerRef.current);\n            }\n            return ({\n                \"SmartAdBanner.useEffect\": ()=>observer.disconnect()\n            })[\"SmartAdBanner.useEffect\"];\n        }\n    }[\"SmartAdBanner.useEffect\"], [\n        paidAd,\n        sessionId,\n        hasTrackedImpression\n    ]);\n    // Initialize Google AdSense when no paid ad is available\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"SmartAdBanner.useEffect\": ()=>{\n            if (isLoading || paidAd || googleAdInitialized.current) return;\n            const container = adContainerRef.current;\n            const adEl = container === null || container === void 0 ? void 0 : container.querySelector(\"ins.adsbygoogle\");\n            if (!adEl) return;\n            // Clear any previous ad content\n            adEl.innerHTML = \"\";\n            const { width, height } = dimensions;\n            // Apply sizing rules based on slot type\n            const isHorizontal = width > height; // Leaderboard, Billboard, Banner types\n            if (isHorizontal && container) {\n                var _container_parentElement;\n                const parentWidth = ((_container_parentElement = container.parentElement) === null || _container_parentElement === void 0 ? void 0 : _container_parentElement.clientWidth) || width;\n                const calculatedWidth = Math.min(parentWidth, width);\n                const calculatedHeight = height / width * calculatedWidth;\n                container.style.width = \"\".concat(calculatedWidth, \"px\");\n                container.style.height = \"\".concat(calculatedHeight, \"px\");\n            } else if (container) {\n                container.style.width = \"\".concat(width, \"px\");\n                container.style.height = \"\".concat(height, \"px\");\n            }\n            if (container) {\n                container.style.overflow = \"hidden\";\n                container.style.position = \"relative\";\n            }\n            // Enhanced Google AdSense loading with comprehensive error handling\n            let googleRetryCount = 0;\n            const loadGoogleAd = {\n                \"SmartAdBanner.useEffect.loadGoogleAd\": ()=>{\n                    try {\n                        // Clear any existing timeout\n                        if (googleAdLoadTimeout.current) {\n                            clearTimeout(googleAdLoadTimeout.current);\n                        }\n                        // Set timeout for Google AdSense loading\n                        googleAdLoadTimeout.current = setTimeout({\n                            \"SmartAdBanner.useEffect.loadGoogleAd\": ()=>{\n                                if (!googleAdInitialized.current) {\n                                    console.error(\"Google AdSense load timeout for slot:\", slotId);\n                                    setGoogleAdError(\"AdSense load timeout\");\n                                    setFallbackMode(\"placeholder\");\n                                    // Log AdSense timeout\n                                    logAdError({\n                                        slotId,\n                                        errorType: \"timeout\",\n                                        errorMessage: \"Google AdSense load timeout\",\n                                        sessionId,\n                                        fallbackUsed: \"placeholder\"\n                                    });\n                                }\n                            }\n                        }[\"SmartAdBanner.useEffect.loadGoogleAd\"], 10000); // 10 second timeout\n                        (window.adsbygoogle = window.adsbygoogle || []).push({});\n                        googleAdInitialized.current = true;\n                        setFallbackMode(\"google\");\n                        // Clear timeout on successful load\n                        if (googleAdLoadTimeout.current) {\n                            clearTimeout(googleAdLoadTimeout.current);\n                        }\n                        // Log successful AdSense load\n                        logAdPerformance({\n                            slotId,\n                            paidAdSuccess: false,\n                            adsenseSuccess: true,\n                            loadTime: Date.now() - loadStartTime,\n                            fallbackUsed: true\n                        });\n                    } catch (e) {\n                        googleRetryCount++;\n                        console.error(\"Google AdSense load error (attempt \".concat(googleRetryCount, \"):\"), e);\n                        if (googleRetryCount < 3) {\n                            setTimeout(loadGoogleAd, 1000 * googleRetryCount); // Progressive delay\n                        } else {\n                            console.error(\"Google AdSense load failed after 3 attempts for slot:\", slotId);\n                            const errorMessage = \"AdSense failed: \".concat(e instanceof Error ? e.message : \"Unknown error\");\n                            setGoogleAdError(errorMessage);\n                            setFallbackMode(\"placeholder\");\n                            // Log AdSense failure\n                            logAdError({\n                                slotId,\n                                errorType: \"adsense_failure\",\n                                errorMessage,\n                                retryCount: googleRetryCount,\n                                sessionId,\n                                fallbackUsed: \"placeholder\"\n                            });\n                        }\n                    }\n                }\n            }[\"SmartAdBanner.useEffect.loadGoogleAd\"];\n            // Only load Google ads if we don't have a paid ad and no placeholder slot\n            if (!googleAdSlot.includes(\"PLACEHOLDER\")) {\n                loadGoogleAd();\n            } else {\n                console.warn(\"Placeholder Google AdSense slot detected for slotId \".concat(slotId, \": \").concat(googleAdSlot));\n                setFallbackMode(\"placeholder\");\n            }\n            return ({\n                \"SmartAdBanner.useEffect\": ()=>{\n                    if (adEl) adEl.innerHTML = \"\";\n                }\n            })[\"SmartAdBanner.useEffect\"];\n        }\n    }[\"SmartAdBanner.useEffect\"], [\n        slotId,\n        isLoading,\n        paidAd\n    ]);\n    // Handle paid ad click with error handling\n    const handlePaidAdClick = async ()=>{\n        if (!paidAd) return;\n        try {\n            // Track the click (don't wait for it to complete)\n            (0,_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.trackAdClick)({\n                ad_id: paidAd.id,\n                session_id: sessionId,\n                country_code: (0,_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.getCountryCode)(),\n                device_type: (0,_lib_api_ad_serving__WEBPACK_IMPORTED_MODULE_1__.getDeviceType)(navigator.userAgent)\n            }).catch((error)=>{\n                console.error(\"Failed to track click:\", error);\n            // Don't block the redirect for tracking failures\n            });\n            // Redirect to target URL immediately\n            window.open(paidAd.target_url, \"_blank\", \"noopener,noreferrer\");\n        } catch (error) {\n            console.error(\"Error handling ad click:\", error);\n            // Still try to redirect even if tracking fails\n            window.open(paidAd.target_url, \"_blank\", \"noopener,noreferrer\");\n        }\n    };\n    // Validate that the required Google AdSense slot is provided\n    if (!googleAdSlot || googleAdSlot.includes(\"PLACEHOLDER\")) {\n        console.error(\"Invalid Google AdSense slot for slotId \".concat(slotId, \": \").concat(googleAdSlot));\n    }\n    // Render fallback placeholder when both paid ads and AdSense fail\n    const renderFallbackPlaceholder = ()=>{\n        const { width, height } = dimensions;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"inline-block \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: \"\".concat(width, \"px\"),\n                    height: \"\".concat(height, \"px\"),\n                    margin: \"0 auto\",\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\",\n                    backgroundColor: \"#f8fafc\",\n                    border: \"2px dashed #e2e8f0\",\n                    borderRadius: \"8px\",\n                    color: \"#64748b\",\n                    fontSize: \"14px\",\n                    textAlign: \"center\",\n                    padding: \"16px\",\n                    boxSizing: \"border-box\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginBottom: \"8px\",\n                            fontSize: \"16px\"\n                        },\n                        children: \"\\uD83D\\uDCE2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                        lineNumber: 361,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontWeight: \"500\",\n                            marginBottom: \"4px\"\n                        },\n                        children: \"Advertisement Space\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"12px\",\n                            opacity: 0.7\n                        },\n                        children: error && googleAdError ? \"Service temporarily unavailable\" : googleAdSlot.includes(\"PLACEHOLDER\") ? \"Slot configuration pending\" : \"Loading advertisement...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                        lineNumber: 363,\n                        columnNumber: 6\n                    }, this),\n                     true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: \"10px\",\n                            marginTop: \"8px\",\n                            opacity: 0.5\n                        },\n                        children: [\n                            \"Slot \",\n                            slotId,\n                            \" • \",\n                            width,\n                            \"\\xd7\",\n                            height\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                        lineNumber: 371,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                lineNumber: 342,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n            lineNumber: 341,\n            columnNumber: 4\n        }, this);\n    };\n    // Show loading state\n    if (isLoading && enablePaidAds) {\n        const { width, height } = dimensions;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"inline-block \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: \"\".concat(width, \"px\"),\n                    height: \"\".concat(height, \"px\"),\n                    margin: \"0 auto\",\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\",\n                    backgroundColor: \"#f3f4f6\",\n                    border: \"1px solid #e5e7eb\",\n                    borderRadius: \"4px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm text-gray-500\",\n                    children: retryCount > 0 ? \"Loading ads... (\".concat(retryCount, \"/\").concat(maxRetries, \")\") : \"Loading ads...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                    lineNumber: 398,\n                    columnNumber: 6\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                lineNumber: 385,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n            lineNumber: 384,\n            columnNumber: 4\n        }, this);\n    }\n    // Show fallback placeholder when both paid ads and AdSense fail\n    if (fallbackMode === \"placeholder\") {\n        return renderFallbackPlaceholder();\n    }\n    // Show error state (only if we have an error and no fallback)\n    if (error && !enablePaidAds && fallbackMode === \"none\") {\n        return renderFallbackPlaceholder();\n    }\n    // Render paid ad\n    if (paidAd) {\n        const { width, height } = dimensions;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"inline-block \".concat(className),\n            ref: adContainerRef,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: \"\".concat(width, \"px\"),\n                    height: \"\".concat(height, \"px\"),\n                    margin: \"0 auto\",\n                    position: \"relative\",\n                    cursor: \"pointer\",\n                    overflow: \"hidden\",\n                    border: \"1px solid #e5e7eb\",\n                    borderRadius: \"4px\"\n                },\n                onClick: handlePaidAdClick,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: paidAd.image_url,\n                        alt: paidAd.title,\n                        style: {\n                            width: \"100%\",\n                            height: \"100%\",\n                            objectFit: \"cover\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                        lineNumber: 435,\n                        columnNumber: 6\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"absolute\",\n                            top: \"4px\",\n                            right: \"4px\",\n                            background: \"rgba(0,0,0,0.7)\",\n                            color: \"white\",\n                            fontSize: \"10px\",\n                            padding: \"2px 4px\",\n                            borderRadius: \"2px\"\n                        },\n                        children: \"Sponsored\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                        lineNumber: 444,\n                        columnNumber: 6\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                lineNumber: 422,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n            lineNumber: 421,\n            columnNumber: 4\n        }, this);\n    }\n    // Render Google AdSense fallback (only if not in placeholder mode)\n    if (fallbackMode === \"google\" || !error && !googleAdError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"inline-block \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: adContainerRef,\n                style: {\n                    minWidth: \"\".concat(dimensions.width, \"px\"),\n                    minHeight: \"\".concat(dimensions.height, \"px\"),\n                    margin: \"0 auto\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ins\", {\n                    className: \"adsbygoogle\",\n                    style: {\n                        display: \"block\"\n                    },\n                    \"data-ad-client\": \"ca-pub-5681407322305640\",\n                    \"data-ad-slot\": googleAdSlot,\n                    \"data-ad-format\": dimensions.width > dimensions.height ? \"horizontal\" : dimensions.width === dimensions.height ? \"rectangle\" : \"auto\",\n                    \"data-full-width-responsive\": \"true\"\n                }, \"\".concat(slotId, \"-\").concat(googleAdSlot), false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                    lineNumber: 475,\n                    columnNumber: 6\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n                lineNumber: 467,\n                columnNumber: 5\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\ads-placements\\\\smart-ad-banner.tsx\",\n            lineNumber: 466,\n            columnNumber: 4\n        }, this);\n    }\n    // Final fallback - render placeholder\n    return renderFallbackPlaceholder();\n}\n_s(SmartAdBanner, \"1JBpCQtIfTae8lbGZzGSXCQ6b20=\");\n_c = SmartAdBanner;\nvar _c;\n$RefreshReg$(_c, \"SmartAdBanner\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ads-placements/smart-ad-banner.tsx\n"));

/***/ })

});