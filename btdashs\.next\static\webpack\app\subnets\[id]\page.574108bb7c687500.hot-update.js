"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/subnets/[id]/page",{

/***/ "(app-pages-browser)/./components/subnets/subnet-profile.tsx":
/*!***********************************************!*\
  !*** ./components/subnets/subnet-profile.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SubnetProfile)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Book,Github,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book.js\");\n/* harmony import */ var _barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Book,Github,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Book,Github,Globe!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var swiper_modules__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! swiper/modules */ \"(app-pages-browser)/./node_modules/swiper/modules/index.mjs\");\n/* harmony import */ var swiper_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! swiper/react */ \"(app-pages-browser)/./node_modules/swiper/swiper-react.mjs\");\n/* harmony import */ var _components_category_tag__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/category-tag */ \"(app-pages-browser)/./components/category-tag.tsx\");\n/* harmony import */ var _components_subnet_documentation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/subnet-documentation */ \"(app-pages-browser)/./components/subnet-documentation.tsx\");\n/* harmony import */ var _components_subnet_github_contribution_graph__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/subnet-github-contribution-graph */ \"(app-pages-browser)/./components/subnet-github-contribution-graph.tsx\");\n/* harmony import */ var _components_subnet_news__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/subnet-news */ \"(app-pages-browser)/./components/subnet-news.tsx\");\n/* harmony import */ var _components_subnet_team__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/subnet-team */ \"(app-pages-browser)/./components/subnet-team.tsx\");\n/* harmony import */ var _components_subnet_validators__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/subnet-validators */ \"(app-pages-browser)/./components/subnet-validators.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _public_tao_logo_svg__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/public/tao-logo.svg */ \"(app-pages-browser)/./public/tao-logo.svg\");\n/* harmony import */ var _components_ads_placements_smart_ad_banner__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ads-placements/smart-ad-banner */ \"(app-pages-browser)/./components/ads-placements/smart-ad-banner.tsx\");\n/* harmony import */ var _components_subnets_subnet_relationship_chart__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/subnets/subnet-relationship-chart */ \"(app-pages-browser)/./components/subnets/subnet-relationship-chart.tsx\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var _subnet_applications__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../subnet-applications */ \"(app-pages-browser)/./components/subnet-applications.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n/* import { useRef, useState } from \"react\"; */ \n\n\n\n\n\n\n\n\n\n\n\n// Import the new chart component\n\n\n\n\nfunction SubnetProfile(param) {\n    let { subnet, metrics, categories, news, products, jobs, events, companies } = param;\n    var _subnet_subnet_ids, _subnet_images, _metrics_github_contributions;\n    /* const [imageError, setImageError] = useState(false);\r\n  const videoRef = useRef<HTMLVideoElement>(null);\r\n  const [isPlaying, setIsPlaying] = useState(false);\r\n  const [isMuted, setIsMuted] = useState(false); */ const netuid = subnet.netuid;\n    var _companies_length, _products_length, _events_length, _jobs_length, _categories_length, _metrics_validators_count, _news_length, _subnet_subnet_ids_length;\n    /* const images = subnet.images?.length\r\n    ? subnet.images\r\n    : [\r\n        \"https://via.placeholder.com/800x400?text=Image+1\",\r\n        \"https://via.placeholder.com/800x400?text=Image+2\",\r\n        \"https://via.placeholder.com/800x400?text=Image+3\",\r\n      ]; */ const data = {\n        companyCount: (_companies_length = companies === null || companies === void 0 ? void 0 : companies.length) !== null && _companies_length !== void 0 ? _companies_length : 0,\n        productCount: (_products_length = products === null || products === void 0 ? void 0 : products.length) !== null && _products_length !== void 0 ? _products_length : 0,\n        eventCount: (_events_length = events === null || events === void 0 ? void 0 : events.length) !== null && _events_length !== void 0 ? _events_length : 0,\n        jobCount: (_jobs_length = jobs === null || jobs === void 0 ? void 0 : jobs.length) !== null && _jobs_length !== void 0 ? _jobs_length : 0,\n        categoryCount: (_categories_length = categories === null || categories === void 0 ? void 0 : categories.length) !== null && _categories_length !== void 0 ? _categories_length : 0,\n        validatorCount: (_metrics_validators_count = metrics === null || metrics === void 0 ? void 0 : metrics.validators_count) !== null && _metrics_validators_count !== void 0 ? _metrics_validators_count : 0,\n        newsCount: (_news_length = news.length) !== null && _news_length !== void 0 ? _news_length : 0,\n        subnetCount: (_subnet_subnet_ids_length = subnet === null || subnet === void 0 ? void 0 : (_subnet_subnet_ids = subnet.subnet_ids) === null || _subnet_subnet_ids === void 0 ? void 0 : _subnet_subnet_ids.length) !== null && _subnet_subnet_ids_length !== void 0 ? _subnet_subnet_ids_length : 0\n    };\n    var _metrics_validators_count1, _metrics_emission;\n    const metricsCards = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardTitle, {\n                            children: \"Price\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-3xl font-bold\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    src: _public_tao_logo_svg__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                                    alt: \"TAO\",\n                                    width: 24,\n                                    height: 24,\n                                    className: \"inline-block pr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 7\n                                }, this),\n                                (metrics === null || metrics === void 0 ? void 0 : metrics.alpha_price_tao) != null && !isNaN(metrics.alpha_price_tao) ? Number(metrics.alpha_price_tao) < 0.01 ? Number(metrics.alpha_price_tao).toFixed(3) : Number(metrics.alpha_price_tao).toFixed(2) : \"0.00\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                lineNumber: 79,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardTitle, {\n                            children: \"Validators\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-3xl font-bold\",\n                            children: (_metrics_validators_count1 = metrics === null || metrics === void 0 ? void 0 : metrics.validators_count) !== null && _metrics_validators_count1 !== void 0 ? _metrics_validators_count1 : 0\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                lineNumber: 95,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardTitle, {\n                            children: \"Emission\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-3xl font-bold\",\n                            children: [\n                                (((_metrics_emission = metrics === null || metrics === void 0 ? void 0 : metrics.emission) !== null && _metrics_emission !== void 0 ? _metrics_emission : 0) / 1e7).toFixed(2),\n                                \"%\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                lineNumber: 104,\n                columnNumber: 4\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardTitle, {\n                            children: \"Miners\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 5\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-3xl font-bold \".concat(subnet.active_miners <= 5 ? \"text-red-500\" : subnet.active_miners <= 15 ? \"text-orange-500\" : \"text-green-500\"),\n                            children: subnet.active_miners || 0\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 6\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 5\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                lineNumber: 113,\n                columnNumber: 4\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n        lineNumber: 78,\n        columnNumber: 3\n    }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-8 px-6 sm:px-8 lg:px-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full mb-8\",\n                    style: {\n                        minHeight: \"90px\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ads_placements_smart_ad_banner__WEBPACK_IMPORTED_MODULE_14__.SmartAdBanner, {\n                        slotId: 7,\n                        googleAdSlot: \"3064454441\",\n                        className: \"mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 6\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 5\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8 grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 rounded-full bg-primary flex items-center justify-center text-black text-3xl font-bold\",\n                                            children: subnet.subnet_symbol || subnet.name.charAt(0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 8\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl font-bold\",\n                                                    children: subnet.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 9\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: [\n                                                        \"Subnet ID: \",\n                                                        netuid\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 9\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: [\n                                                        \"Coldkey:\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"https://taostats.io/account/\".concat(subnet.sub_address_pkey, \"/?utm_source=dynamictoamarketcap&utm_medium=referral&utm_campaign=subnet_profile\"),\n                                                            target: \"_blank\",\n                                                            rel: \"noopener noreferrer\",\n                                                            className: \"text-xs text-muted-foreground underline hover:text-primary\",\n                                                            children: subnet.sub_address_pkey\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 10\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 9\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-3 flex flex-wrap gap-2 text-muted-foreground\",\n                                                    children: categories && categories.length > 0 ? categories.map((category, id)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_category_tag__WEBPACK_IMPORTED_MODULE_4__.CategoryTag, {\n                                                            category: category.name\n                                                        }, id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 13\n                                                        }, this)) : null\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 9\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 8\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"my-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_17__.Markdown, {\n                                        children: subnet.description_short\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 8\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-4 mb-8\",\n                                    children: subnet.white_paper ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                        asChild: true,\n                                        size: \"sm\",\n                                        className: \"gap-2\",\n                                        variant: \"default\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: subnet.white_paper,\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 11\n                                                }, this),\n                                                \"Read White Paper\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 10\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 9\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                        size: \"sm\",\n                                        className: \"gap-2\",\n                                        disabled: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 10\n                                            }, this),\n                                            \"White Paper Unavailable\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 9\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 7\n                                }, this),\n                                subnet.images || subnet.main_video_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2 overflow-hidden\",\n                                    children: metricsCards\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 8\n                                }, this) : null\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 6\n                        }, this),\n                        subnet.main_video_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative pb-[56.25%] h-0 overflow-hidden rounded-lg shadow-lg border border-slate-200 dark:border-slate-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                                        className: \"absolute left-0 w-full h-full\",\n                                        src: subnet.main_video_url,\n                                        title: \"Subnet video\",\n                                        frameBorder: \"0\",\n                                        allow: \"autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\n                                        allowFullScreen: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 9\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 8\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-4 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: subnet.website_perm || \"https://subnet-website.example.com\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-12 h-12 rounded-full bg-blue-50 dark:bg-blue-900/30 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-6 w-6 text-blue-600 dark:text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-grow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-slate-900 dark:text-slate-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors\",\n                                                            children: \"Official Website\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 222,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-500 dark:text-slate-400 truncate\",\n                                                            children: subnet.website_perm ? \"\".concat(subnet.website_perm.replace(\"https://www.\", \"\").slice(0, 30)).concat(subnet.website_perm.replace(\"https://www.\", \"\").length > 40 ? \"...\" : \"\") : \"subnet-website.example.com\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 9\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: subnet.github_repo || \"https://github.com/example/subnet-repo\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-12 h-12 rounded-full bg-purple-50 dark:bg-purple-900/30 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-6 w-6 text-purple-600 dark:text-purple-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-grow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-slate-900 dark:text-slate-100 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors\",\n                                                            children: \"GitHub Repository\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 247,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-500 dark:text-slate-400 truncate\",\n                                                            children: subnet.github_repo ? \"\".concat(subnet.github_repo.replace(\"https://github.com/\", \"\").slice(0, 30)).concat(subnet.github_repo.replace(\"https://github.com/\", \"\").length > 40 ? \"...\" : \"\") : \"github.com/example/subnet-repo\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 250,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 9\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 8\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 7\n                        }, this) : !subnet.main_video_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                subnet.images ? null : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"gap-2 overflow-hidden\",\n                                    children: metricsCards\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 32\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"rounded-lg overflow-hidden shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_3__.Swiper, {\n                                        modules: [\n                                            swiper_modules__WEBPACK_IMPORTED_MODULE_2__.Navigation,\n                                            swiper_modules__WEBPACK_IMPORTED_MODULE_2__.Pagination\n                                        ],\n                                        navigation: true,\n                                        pagination: {\n                                            clickable: true\n                                        },\n                                        spaceBetween: 10,\n                                        slidesPerView: 1,\n                                        className: \"w-full h-full\",\n                                        children: (_subnet_images = subnet.images) === null || _subnet_images === void 0 ? void 0 : _subnet_images.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_3__.SwiperSlide, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: image,\n                                                    alt: \"Subnet Image \".concat(index + 1),\n                                                    width: 800,\n                                                    height: 400,\n                                                    className: \"w-full h-auto object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 12\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 11\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 9\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 8\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-4 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: subnet.website_perm || \"https://subnet-website.example.com\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-12 h-12 rounded-full bg-blue-50 dark:bg-blue-900/30 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-6 w-6 text-blue-600 dark:text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-grow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-slate-900 dark:text-slate-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors\",\n                                                            children: \"Official Website\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-500 dark:text-slate-400 truncate\",\n                                                            children: subnet.website_perm ? \"\".concat(subnet.website_perm.replace(\"https://www.\", \"\").slice(0, 30)).concat(subnet.website_perm.replace(\"https://www.\", \"\").length > 40 ? \"...\" : \"\") : \"subnet-website.example.com\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 305,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 9\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: subnet.github_repo || \"https://github.com/example/subnet-repo\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-12 h-12 rounded-full bg-purple-50 dark:bg-purple-900/30 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-6 w-6 text-purple-600 dark:text-purple-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-grow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-slate-900 dark:text-slate-100 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors\",\n                                                            children: \"GitHub Repository\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-500 dark:text-slate-400 truncate\",\n                                                            children: subnet.github_repo ? \"\".concat(subnet.github_repo.replace(\"https://github.com/\", \"\").slice(0, 30)).concat(subnet.github_repo.replace(\"https://github.com/\", \"\").length > 40 ? \"...\" : \"\") : \"github.com/example/subnet-repo\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 330,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 9\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 8\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 7\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \" gap-4 overflow-hidden max-[60px]\",\n                                    children: metricsCards\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 8\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-4 pt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: subnet.website_perm || \"https://subnet-website.example.com\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-12 h-12 rounded-full bg-blue-50 dark:bg-blue-900/30 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-6 w-6 text-blue-600 dark:text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-grow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-slate-900 dark:text-slate-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors\",\n                                                            children: \"Official Website\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-500 dark:text-slate-400 truncate\",\n                                                            children: subnet.website_perm ? \"\".concat(subnet.website_perm.replace(\"https://www.\", \"\").slice(0, 30)).concat(subnet.website_perm.replace(\"https://www.\", \"\").length > 40 ? \"...\" : \"\") : \"subnet-website.example.com\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 9\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: subnet.github_repo || \"https://github.com/example/subnet-repo\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"group flex items-center gap-3 p-4 rounded-lg bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-md hover:shadow-lg transition-all duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 w-12 h-12 rounded-full bg-purple-50 dark:bg-purple-900/30 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Book_Github_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-6 w-6 text-purple-600 dark:text-purple-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-grow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-slate-900 dark:text-slate-100 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors\",\n                                                            children: \"GitHub Repository\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 385,\n                                                            columnNumber: 11\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-500 dark:text-slate-400 truncate\",\n                                                            children: subnet.github_repo ? \"\".concat(subnet.github_repo.replace(\"https://github.com/\", \"\").slice(0, 30)).concat(subnet.github_repo.replace(\"https://github.com/\", \"\").length > 40 ? \"...\" : \"\") : \"github.com/example/subnet-repo\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 11\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 9\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 8\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 7\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ads_placements_smart_ad_banner__WEBPACK_IMPORTED_MODULE_14__.SmartAdBanner, {\n                                slotId: 9,\n                                googleAdSlot: \"9765594162\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 7\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 407,\n                            columnNumber: 6\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 5\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-7 gap-4 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-span-5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_subnet_github_contribution_graph__WEBPACK_IMPORTED_MODULE_6__.SubnetGithubContributionGraph, {\n                                className: \"h-[360px]\",\n                                contributions: (metrics === null || metrics === void 0 ? void 0 : (_metrics_github_contributions = metrics.github_contributions) === null || _metrics_github_contributions === void 0 ? void 0 : _metrics_github_contributions.data) || []\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                lineNumber: 416,\n                                columnNumber: 7\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 415,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-[360px] col-span-2 overflow-visible\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_subnets_subnet_relationship_chart__WEBPACK_IMPORTED_MODULE_15__.SubnetRelationshipChart, {\n                                subnetId: subnet.name,\n                                data: data,\n                                className: \"h-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 7\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 423,\n                            columnNumber: 6\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                    lineNumber: 413,\n                    columnNumber: 5\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-0 mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_subnet_applications__WEBPACK_IMPORTED_MODULE_16__.SubnetApplications, {\n                        products: products\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 430,\n                        columnNumber: 6\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                    lineNumber: 429,\n                    columnNumber: 5\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full mb-8 flex justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ads_placements_smart_ad_banner__WEBPACK_IMPORTED_MODULE_14__.SmartAdBanner, {\n                        slotId: 10,\n                        googleAdSlot: \"6623305948\",\n                        className: \"mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 435,\n                        columnNumber: 6\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                    lineNumber: 434,\n                    columnNumber: 5\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.Tabs, {\n                    defaultValue: \"overview\",\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsList, {\n                            className: \"flex flex-wrap\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsTrigger, {\n                                    value: \"overview\",\n                                    children: \"Overview\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsTrigger, {\n                                    value: \"team\",\n                                    children: \"Team\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 442,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsTrigger, {\n                                    value: \"documentation\",\n                                    children: \"Documentation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsTrigger, {\n                                    value: \"validators\",\n                                    children: \"Validators\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 444,\n                                    columnNumber: 7\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsTrigger, {\n                                    value: \"news\",\n                                    children: \"News\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                    lineNumber: 445,\n                                    columnNumber: 7\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 440,\n                            columnNumber: 6\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-1 gap-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsContent, {\n                                        value: \"overview\",\n                                        className: \"space-y-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardTitle, {\n                                                        className: \"text-lg\",\n                                                        children: \"Key Features\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 10\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_11__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid md:grid-cols-2 gap-4\",\n                                                        children: subnet.key_features && subnet.key_features.length > 0 ? subnet.key_features[0].map((feature, id)=>feature && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2 pb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium\",\n                                                                        children: feature.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                                        lineNumber: 462,\n                                                                        columnNumber: 17\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-muted-foreground\",\n                                                                        children: feature.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                                        lineNumber: 463,\n                                                                        columnNumber: 17\n                                                                    }, this)\n                                                                ]\n                                                            }, id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 16\n                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-muted-foreground\",\n                                                            children: \"No key features available.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                            lineNumber: 470,\n                                                            columnNumber: 13\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                        lineNumber: 456,\n                                                        columnNumber: 11\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 10\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 9\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 8\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsContent, {\n                                        value: \"team\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_subnet_team__WEBPACK_IMPORTED_MODULE_8__.SubnetTeam, {\n                                            subnet: subnet\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 9\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 477,\n                                        columnNumber: 8\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsContent, {\n                                        value: \"documentation\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_subnet_documentation__WEBPACK_IMPORTED_MODULE_5__.SubnetDocumentation, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 9\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 480,\n                                        columnNumber: 8\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsContent, {\n                                        value: \"validators\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_subnet_validators__WEBPACK_IMPORTED_MODULE_9__.SubnetValidators, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 9\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 8\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_12__.TabsContent, {\n                                        value: \"news\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_subnet_news__WEBPACK_IMPORTED_MODULE_7__.SubnetNews, {\n                                            news: news\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                            lineNumber: 487,\n                                            columnNumber: 9\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 8\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                                lineNumber: 449,\n                                columnNumber: 7\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                            lineNumber: 448,\n                            columnNumber: 6\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                    lineNumber: 439,\n                    columnNumber: 5\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 mt-8 mb-8 min-h-[90px] w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ads_placements_smart_ad_banner__WEBPACK_IMPORTED_MODULE_14__.SmartAdBanner, {\n                        slotId: 8,\n                        googleAdSlot: \"9249469283\",\n                        className: \"w-full h-full\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                        lineNumber: 495,\n                        columnNumber: 6\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n                    lineNumber: 494,\n                    columnNumber: 5\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\G_PROG\\\\Nicolas\\\\btdash-ecosystem\\\\btdashs\\\\components\\\\subnets\\\\subnet-profile.tsx\",\n            lineNumber: 136,\n            columnNumber: 4\n        }, this)\n    }, void 0, false);\n}\n_c = SubnetProfile;\nvar _c;\n$RefreshReg$(_c, \"SubnetProfile\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/subnets/subnet-profile.tsx\n"));

/***/ })

});