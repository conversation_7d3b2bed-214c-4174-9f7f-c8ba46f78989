{"/api/news/route": "app/api/news/route.js", "/api/categories/route": "app/api/categories/route.js", "/api/network-stats/latest/route": "app/api/network-stats/latest/route.js", "/api/products/route": "app/api/products/route.js", "/api/jobs/route": "app/api/jobs/route.js", "/api/companies/route": "app/api/companies/route.js", "/api/events/route": "app/api/events/route.js", "/api/subnets/[id]/route": "app/api/subnets/[id]/route.js", "/api/subnet-metrics/[id]/route": "app/api/subnet-metrics/[id]/route.js", "/api/user/me/route": "app/api/user/me/route.js", "/_not-found/page": "app/_not-found/page.js", "/subnets/[id]/page": "app/subnets/[id]/page.js"}